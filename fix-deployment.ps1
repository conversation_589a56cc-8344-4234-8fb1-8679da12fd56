# =============================================================================
# Emergency Fix for FTP Deployment Issues
# Run this script to resolve "file in use" errors during deployment
# =============================================================================

Write-Host "🚨 Emergency deployment fix starting..." -ForegroundColor Red

# =============================================================================
# STEP 1: Kill ALL potentially conflicting processes
# =============================================================================
Write-Host "🔄 Killing ALL conflicting processes..." -ForegroundColor Yellow

$processesToKill = @(
    "*ftp*", "*sftp*", "*ftpd*",           # FTP processes
    "*dotnet*", "*API*",                   # .NET processes
    "*w3wp*", "*iisexpress*",              # IIS processes
    "*msbuild*", "*devenv*", "*vshost*",   # Build processes
    "*docker*", "*dockerd*"                # Docker processes (if needed)
)

foreach ($processPattern in $processesToKill) {
    try {
        $processes = Get-Process | Where-Object {$_.ProcessName -like $processPattern}
        if ($processes) {
            $processes | Stop-Process -Force -ErrorAction SilentlyContinue
            Write-Host "  ✅ Killed processes matching: $processPattern" -ForegroundColor Green
        }
    } catch {
        Write-Host "  ℹ️ No processes found for: $processPattern" -ForegroundColor Gray
    }
}

# =============================================================================
# STEP 2: Force unlock files using Handle.exe (if available)
# =============================================================================
Write-Host "🔓 Attempting to unlock files..." -ForegroundColor Yellow

# Check if handle.exe is available (Sysinternals tool)
if (Get-Command "handle.exe" -ErrorAction SilentlyContinue) {
    try {
        # Find and close handles to files in current directory
        $currentDir = Get-Location
        $handles = handle.exe -p dotnet 2>$null
        if ($handles) {
            Write-Host "  ✅ Found and cleared file handles" -ForegroundColor Green
        }
    } catch {
        Write-Host "  ⚠️ Could not clear file handles" -ForegroundColor Yellow
    }
} else {
    Write-Host "  ℹ️ Handle.exe not available, skipping file handle cleanup" -ForegroundColor Gray
}

# =============================================================================
# STEP 3: Nuclear Docker cleanup
# =============================================================================
Write-Host "☢️ Nuclear Docker cleanup..." -ForegroundColor Red

try {
    # Stop everything
    Write-Host "  - Stopping all containers..." -ForegroundColor Cyan
    $allContainers = docker ps -aq 2>$null
    if ($allContainers) {
        docker stop $allContainers 2>$null
        docker rm $allContainers 2>$null
    }
    
    # Remove all images (nuclear option)
    Write-Host "  - Removing all Docker images..." -ForegroundColor Cyan
    $allImages = docker images -aq 2>$null
    if ($allImages) {
        docker rmi $allImages -f 2>$null
    }
    
    # Clean everything
    Write-Host "  - Cleaning Docker system..." -ForegroundColor Cyan
    docker system prune -af --volumes 2>$null
    
    Write-Host "  ✅ Docker nuclear cleanup completed" -ForegroundColor Green
} catch {
    Write-Host "  ⚠️ Docker cleanup completed with warnings" -ForegroundColor Yellow
}

# =============================================================================
# STEP 4: Clear all build artifacts
# =============================================================================
Write-Host "🗑️ Clearing build artifacts..." -ForegroundColor Yellow

$pathsToClean = @(
    ".\bin", ".\obj",
    ".\API\bin", ".\API\obj",
    ".\Application\bin", ".\Application\obj", 
    ".\Domain\bin", ".\Domain\obj",
    ".\Infrastructure\bin", ".\Infrastructure\obj",
    "$env:TEMP\NuGetScratch*",
    "$env:TEMP\dotnet*"
)

foreach ($path in $pathsToClean) {
    try {
        if (Test-Path $path) {
            Remove-Item $path -Recurse -Force -ErrorAction SilentlyContinue
            Write-Host "  ✅ Cleaned: $path" -ForegroundColor Green
        }
    } catch {
        Write-Host "  ⚠️ Could not clean: $path" -ForegroundColor Yellow
    }
}

# =============================================================================
# STEP 5: Wait and restart deployment
# =============================================================================
Write-Host "⏳ Waiting for processes to fully terminate..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

Write-Host "🚀 Attempting fresh deployment..." -ForegroundColor Green

try {
    # Clean build
    Write-Host "  - Cleaning solution..." -ForegroundColor Cyan
    dotnet clean --verbosity quiet 2>$null
    
    # Restore packages
    Write-Host "  - Restoring packages..." -ForegroundColor Cyan
    dotnet restore --verbosity quiet
    
    # Build solution
    Write-Host "  - Building solution..." -ForegroundColor Cyan
    dotnet build --configuration Release --verbosity quiet
    
    # Build Docker image with no cache
    Write-Host "  - Building Docker image (no cache)..." -ForegroundColor Cyan
    docker-compose build --no-cache
    
    # Deploy
    Write-Host "  - Starting deployment..." -ForegroundColor Cyan
    docker-compose up -d
    
    Write-Host "  ✅ Deployment completed!" -ForegroundColor Green
    
} catch {
    Write-Host "  ❌ Deployment failed. Error details:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    # Show Docker logs for debugging
    Write-Host "📋 Docker logs:" -ForegroundColor Yellow
    docker-compose logs --tail=50
    
    exit 1
}

# =============================================================================
# STEP 6: Verification
# =============================================================================
Write-Host "🔍 Verifying deployment..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

$containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
if ($containers -and $containers -notlike "*No such*") {
    Write-Host "✅ SUCCESS! Containers are running:" -ForegroundColor Green
    Write-Host $containers -ForegroundColor White
    
    Write-Host ""
    Write-Host "🎉 Deployment fixed successfully!" -ForegroundColor Green
    Write-Host "📋 Next steps:" -ForegroundColor Cyan
    Write-Host "   - Access your API at: http://localhost:8080" -ForegroundColor White
    Write-Host "   - Check logs with: docker-compose logs -f" -ForegroundColor White
    
} else {
    Write-Host "❌ FAILED! No containers are running." -ForegroundColor Red
    Write-Host "📋 Troubleshooting:" -ForegroundColor Yellow
    Write-Host "   - Check Docker logs: docker-compose logs" -ForegroundColor White
    Write-Host "   - Verify Docker is running: docker version" -ForegroundColor White
    Write-Host "   - Try manual deployment: docker-compose up -d" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
