using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain
{
    public interface IRepository<T> where T : class
    {
        T Get(int id);
        IEnumerable<T> GetAll();
        void Add(T entity);
        void Add(List<T> entity);
        void Update(T entity);
        void Delete(int id);
        void Delete(T entity);
        T GetById(long Id);
        IQueryable<T> GetAllQuery();
        IQueryable<T> GetAllQurAsync();

        //IEnumerable<T> GetByUserId(string UserId);
    }

}
