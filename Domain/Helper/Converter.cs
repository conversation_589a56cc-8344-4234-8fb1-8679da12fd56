using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;


namespace Domain.Helper
{
    public  class Converter
    {
        public  Int32 ConvertToInt32(Object value)
        {
            Int32 result = 0;
            if (value != null)
            {
                Int32.TryParse(value.ToString(), out result);
            }
            return result;

        }
        public  double ConvertToDouble(Object value)
        {
            double result = 0;
            if (value != null)
            {
                double.TryParse(value.ToString(), out result);
            }
            return result;

        }
        public  long ConvertToLong(Object value)
        {
            long result = 0;
            if (value != null)
            {
                long.TryParse(value.ToString(), out result);
            }
            return result;

        }
        public  Boolean ConvertToBoolean(Object value)
        {
            Boolean result = false;
            //if (value != null)
            //{
            //    Boolean.TryParse(value.ToString(), out result);
            //}
            var val = value.ToString().ToLower();
            switch (val)
            {
                case "1":
                    result = true;
                    break;
                case "on":
                    result = true;
                    break;
                case "true":
                    result = true;
                    break;

                default:
                    break;
            }
            return result;
        }
        public  int? ConvertToNullableInt32(Object value)
        {
            Int32? result = null;
            if (value != null)
            {
                int val = 0;
                if (Int32.TryParse(value.ToString(), out val))
                {
                    result = val;
                }
            }
            return result;

        }
        //public  Color ConvertColorFromString(String value)
        //{
        //    ColorConverter colorConvert = new ColorConverter();
        //    return (Color)colorConvert.ConvertFromString(value);
        //}
        public  DateTime? ConvertToNullableDateTime(Object value)
        {
            var date = ConvertToDateTime(value);
            if (date == DateTime.MinValue)
                return null;
            return date;

        }
        public  Decimal ConvertToDecimal(Object value)
        {
            Decimal result = 0;
            if (value != null)
            {
                Decimal.TryParse(value.ToString(), out result);
            }
            return result;
        }
        public  DateTime ConvertToDateTime(Object value)
        {
            DateTime result = DateTime.MinValue;
            if (value != null)
            {
                DateTime.TryParse(value.ToString(), out result);
            }
            return result;
        }
        public  String ConvertToString(Object value)
        {
            String result = String.Empty;
            if (value != null)
            {
                result = value.ToString().Trim();
            }
            return result;
        }
        public  Byte[] ConvertToBuffer(Object value)
        {
            if (value != null)
            {
                return value as Byte[];
            }
            return null;
        }
        //public  String ConvertToUrlString(Object value)
        //{
        //    String result = String.Empty;
        //    if (value != null)
        //    {
        //        result = value.ToString();
        //        if (!String.IsNullOrEmpty(result))
        //        {
        //            result = HttpContext.Current.Server.MapPath(result);
        //        }
        //    }
        //    return result;
        //}
        public  DataRow ConvertObjectToDataRow(Object value, DataTable targetTable)
        {
            if (targetTable != null && value != null)
            {
                DataRow row = targetTable.NewRow();
                DataColumn targetColumn = null;
                if (value is DataRowView)
                {
                    foreach (DataColumn col in targetTable.Columns)
                    {
                        try
                        {
                            row[col.ColumnName] = (value as DataRowView).Row[col.ColumnName];
                        }
                        catch
                        {
                            row[col.ColumnName] = (value as DataRowView).Row[col.Caption];
                        }
                    }
                }
                else if (value is DbDataRecord)
                {
                    DbDataRecord record = (value as DbDataRecord);
                    for (Int32 index = 0; index < record.FieldCount; index++)
                    {
                        try
                        {
                            row[record.GetName(index)] = record.GetValue(index);
                        }
                        catch
                        {
                        }
                    }
                }
                else
                {
                    foreach (PropertyInfo property in value.GetType().GetProperties())
                    {
                        targetColumn = targetTable.Columns.Cast<DataColumn>().FirstOrDefault(col => col.ColumnName == property.Name);
                        if (targetColumn != null)
                        {
                            row[targetColumn.ColumnName] = property.GetValue(value, null);
                        }
                    }
                }
                return row;
            }
            return null;
        }
        public  DataTable ListToDataTable<T>(List<T> list)
        {
            DataTable dt = new DataTable();

            foreach (System.Reflection.PropertyInfo info in typeof(T).GetProperties())
            {
                dt.Columns.Add(new DataColumn(info.Name, info.PropertyType));
            }
            foreach (T t in list)
            {
                DataRow row = dt.NewRow();
                foreach (System.Reflection.PropertyInfo info in typeof(T).GetProperties())
                {
                    row[info.Name] = info.GetValue(t, null);
                }
                dt.Rows.Add(row);
            }
            return dt;
        }
        public  Byte[] ConvertImageToBinary(String imagePath)
        {
            Byte[] buffer = null;
            try
            {
                FileInfo info = new FileInfo(imagePath);
                using (BinaryReader reader = new BinaryReader(File.OpenRead(imagePath)))
                {
                    buffer = reader.ReadBytes((Int32)info.Length);
                    reader.Close();
                }
            }
            catch (Exception)
            {
            }
            return buffer;
        }
        public  List<T> ConvertDataTable<T>(DataTable dt)
        {
            List<T> data = new List<T>();
            foreach (DataRow row in dt.Rows)
            {
                T item = GetItem<T>(row);
                data.Add(item);
            }
            return data;
        }
        public  T GetItem<T>(DataRow dr)
        {
            Type temp = typeof(T);
            T obj = Activator.CreateInstance<T>();

            foreach (DataColumn column in dr.Table.Columns)
            {

                foreach (PropertyInfo pro in temp.GetProperties())
                {

                    if (pro.Name == column.ColumnName)
                    {

                        if (dr[column.ColumnName] == DBNull.Value)
                        {

                            pro.SetValue(obj, null, null);
                        }
                        else
                        {
                            pro.SetValue(obj, dr[column.ColumnName], null);
                        }
                    }
                    else
                        continue;
                }
            }
            return obj;
        }
    }
}
