using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Helper
{
    public static class Converters
    {
        // converting from un known value to string
        public static string ToString(object value)
        {
            return value + "";
        }

        //convert from unknown value to deceimal
        public static decimal ToDecimal(object value)
        {
            decimal result = 0;
            decimal.TryParse(value + "", out result);
            return result;
        }


        //convert from decimal db2 date value to date
        public static DateTime ToDate(decimal value)
        {
            int year = int.Parse(value.ToString().Substring(0, 4));
            int month = int.Parse(value.ToString().Substring(4, 2));
            int day = int.Parse(value.ToString().Substring(6, 2));
            DateTime result = new DateTime(year, month, day);
            return result;
        }


        //convert from unknown value to integer
        public static int ToInt(object value)
        {
            int result = 0;
            int.TryParse(value + "", out result);
            return result;
        }

        //convert from unknown value to integer
        public static long ToLong(object value)
        {
            long result = 0;
            long.TryParse(value + "", out result);
            return result;
        }

        //convert from boolean to decimal
        public static decimal ToDecimal(bool value)
        {
            decimal result = value ? 1 : 0;

            return result;
        }

        //convert from Date to IBM Db2 decimal date
        public static decimal ToDb2Date(DateTime value)
        {
            decimal result = decimal.Parse(value.ToString("yyyyMMdd"));
            return result;
        }

        //convert from unknown value to boolean
        public static bool ToBool(object value)
        {
            decimal result = 0;
            decimal.TryParse(value + "", out result);
            return result == 1;
        }
        public static DateTime? ConvertToNullableDateTime(Object value)
        {
            var date = ConvertToDateTime(value);
            if (date == DateTime.MinValue)
                return null;
            return date;

        }

        public static int ConvertToInt(Object value)
        {
            int result = 0;
            int.TryParse(value + "", out result);
            return result;

        }

        public static float ConvertTofloat(Object value)
        {
            float result = 0;
            float.TryParse(value + "", out result);
            return result;

        }
        public static Guid ConvertToGuid(Object value)
        {
            Guid result = default(Guid);
            Guid.TryParse(value + "", out result);
            return result;

        }

        public static DateTime ConvertToDateTime(Object value)
        {
            DateTime result = DateTime.MinValue;
            if (value != null)
            {
                DateTime.TryParse(value.ToString(), out result);
            }
            return result;
        }

        public static string[] Chop(string value, int length, int index)
        {
            int strLength = value.Length;
            int strCount = index;//(strLength + length - 1) / length;
            string[] result = new string[strCount];
            for (int i = 0; i < strCount; ++i)
            {
                if (strLength > 0)
                {
                    result[i] = value.Substring(i * length, Math.Min(length, strLength));
                    strLength -= length;

                }
            }
            return result;
        }

        public static string[] ArrayFromEnter(string value, int index)
        {

            List<string> split = value.Split(new[] { "\r\n", Environment.NewLine }, StringSplitOptions.None).ToList();

            //for (int i = 0; i < split.Count; i++)
            //{
            //    split[i]
            //}
            int currentndex = index - split.Count;
            for (int i = 0; i < currentndex; i++)
            {
                split.Add("");
            }
            return split.ToArray();
        }
        public static T GetAttributeFrom<T>(this object instance, string propertyName) where T : Attribute
        {
            var attrType = typeof(T);
            var property = instance.GetType().GetProperty(propertyName);
            return (T)property.GetCustomAttributes(attrType, false).First();
        }

        public static DateTime Db2Time(decimal time)
        {
            DateTime currDate = DateTime.Now;
            string timeString = time.ToString();
            //if (time != 0)
            //{
            if (timeString.Length < 6)
            {
                decimal maxLength = 6 - timeString.Length;
                for (int i = 0; i < maxLength; i++)
                {
                    timeString = "0" + timeString;
                }
            }
            currDate = DateTime.ParseExact(timeString, "HHmmss",
                               System.Globalization.CultureInfo.InvariantCulture);
            //}

            return currDate;
        }

        public static DateTime Db2Date(decimal date)
        {
            DateTime currDate = DateTime.Now;
            try
            {
                string timeString = date.ToString();
                if (date == 99999999)
                {
                    currDate = DateTime.MaxValue;
                }
                else if (date == 00000000)
                {
                    currDate = DateTime.Now;
                }
                else
                if (date != 0)
                {

                    currDate = DateTime.ParseExact(timeString, "yyyyMMdd",
                                       System.Globalization.CultureInfo.InvariantCulture);
                }

            }
            catch (Exception)
            {

                return DateTime.Now;
            }

            return currDate;
        }


        public static string ConvertArabicNumerals(this string input)
        {
            //if (new string[] { "ar-lb", "ar-SA" }
            //      .Contains(Thread.CurrentThread.CurrentCulture.Name))
            //{
            return input.Replace('\u06f0', '0')
                    .Replace('\u0660', '0')
                    .Replace('\u06f1', '1')
                    .Replace('\u0661', '1')
                    .Replace('\u06f2', '2')
                    .Replace('\u0662', '2')
                    .Replace('\u06f3', '3')
                    .Replace('\u0663', '3')
                    .Replace('\u06f4', '4')
                    .Replace('\u0664', '4')
                    .Replace('\u06f5', '5')
                    .Replace('\u0665', '5')
                    .Replace('\u06f6', '6')
                    .Replace('\u0666', '6')
                    .Replace('\u06f7', '7')
                    .Replace('\u0667', '7')
                    .Replace('\u06f8', '8')
                    .Replace('\u0668', '8')
                    .Replace('\u0669', '9')
                    .Replace('\u06f9', '9')
                    .Replace('۰', '0')
                    .Replace('۱', '1')
                    .Replace('۲', '2')
                    .Replace('۳', '3')
                    .Replace('٤', '4')
                    .Replace('٥', '5')
                    .Replace('٦', '6')
                    .Replace('۷', '7')
                    .Replace('۸', '8')
                    .Replace('۹', '9');
            //}
            //else return input;
        }
        public static string ConvertArabicNumeral(this string input)
        {
            //if (new string[] {  "ar-SA", "en-US" }
            //      .Contains(Thread.CurrentThread.CurrentCulture.Name))
            //{
            return input.Replace('۰', '0')
                    .Replace('۱', '1')
                    .Replace('۲', '2')
                    .Replace('۳', '3')
                    .Replace('٤', '4')
                    .Replace('٥', '5')
                    .Replace('٦', '6')
                    .Replace('۷', '7')
                    .Replace('۸', '8')
                    .Replace('۹', '9');
            //}
            //else return input;
        }
        public static string ToUTF8(this string text)
        {
            return Encoding.UTF8.GetString(Encoding.Default.GetBytes(text));
        }
    }
}
