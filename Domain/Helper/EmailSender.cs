using System.Net;
using System.Net.Mail;
using Microsoft.Extensions.Configuration;

namespace Domain.Helper;

public class EmailSender(IConfiguration configuration)
{
    public void Sender(EmailOtp entity)
    {
        var smtpSection = configuration.GetSection("SmtpSettings");

        var smtpClient = new SmtpClient(smtpSection["Host"])
        {
            Port = int.Parse(smtpSection["Port"]!),
            EnableSsl = bool.Parse(smtpSection["EnableSsl"]!),
            UseDefaultCredentials = false,
            Credentials = new NetworkCredential(smtpSection["Username"], smtpSection["Password"]),
            DeliveryMethod = SmtpDeliveryMethod.Network,
            Timeout = 30000 // 30 seconds timeout
        };
        // Create an improved plain text version with conditional OTP/Password logic
        // Determine if using OTP or Password
        var isOtp = !string.IsNullOrEmpty(entity.Otp);
        var credentials = isOtp
            ? $"Your temporary OTP is: {entity.Otp}"
            : $"Your temporary password is: {entity.Password}";
        var credentialType = isOtp ? "OTP" : "password";
        var actionText = isOtp ? "use this OTP to complete your login" : "change this password immediately";

        var htmlBody = $@"
            <!DOCTYPE html PUBLIC ""-//W3C//DTD XHTML 1.0 Transitional//EN"" ""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"">
            <html xmlns=""http://www.w3.org/1999/xhtml"">
            <head>
                <meta http-equiv=""Content-Type"" content=""text/html; charset=UTF-8"" />
                <meta name=""viewport"" content=""width=device-width, initial-scale=1.0""/>
                <title>{credentialType} Reset</title>
            </head>
            <body style=""margin: 0; padding: 0; font-family: Arial, sans-serif; line-height: 1.6; color: #333333;"">
                <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"">
                    <tr>
                        <td bgcolor=""#ffffff"" align=""center"">
                            <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""600"" style=""max-width: 600px;"">
                                <tr>
                                    <td align=""center"" style=""padding: 20px 0;"">
                                        <h1 style=""color: #0066cc; margin: 0;"">Carry & Go</h1>
                                    </td>
                                </tr>
                                <tr>
                                    <td bgcolor=""#f9f9f9"" style=""padding: 20px; border-radius: 5px; border-left: 4px solid #0066cc;"">
                                        <p>Hello {entity.UserName},</p>
                                        <p>We received a request to reset your {credentialType.ToLower()} for your Carry & Go account.</p>
                                        <p><strong>{credentials}</strong></p>
                                        <p>Please {actionText} for security reasons.</p>
                                        <p>If you didn't request this {credentialType.ToLower()} reset, please contact our support team immediately.</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style=""padding: 20px; text-align: center; font-size: 14px; color: #666666;"">
                                        <p>This is an automated message, please do not reply directly to this email.</p>
                                        <p>&copy; {DateTime.Now.Year} Carry & Go. All rights reserved.</p>
                                        <p>
                                            <a href=""#"" style=""color: #0066cc; text-decoration: none;"">Privacy Policy</a> | 
                                            <a href=""#"" style=""color: #0066cc; text-decoration: none;"">Terms of Service</a>
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </body>
            </html>";


        // Create an improved plain text version that's properly formatted
        var textBody = $@"Carry & Go - Password Reset

                Hello {entity.UserName},

                We received a request to reset your password for your Carry & Go account.

                {credentials}

                Please log in to your account and change this password immediately for security reasons.

                If you didn't request this password reset, please contact our support team immediately.

                --
                This is an automated message, please do not reply directly to this email.
                © {DateTime.Now.Year} Carry & Go. All rights reserved.
                https://carryandgo.com";

        // Add proper MIME alternative view setup for better email client compatibility
        var mailMessage = new MailMessage
        {
            From = new MailAddress(smtpSection["Username"]!, "Carry & Go Support"),
            Subject = "Carry & Go - Password Reset Notification",
            IsBodyHtml = true
        };

        // Add the recipient
        mailMessage.To.Add(entity.Email!);

        // Add the plain text version first (important for proper MIME structure)
        var plainTextView =
            AlternateView.CreateAlternateViewFromString(textBody, System.Text.Encoding.UTF8, "text/plain");
        mailMessage.AlternateViews.Add(plainTextView);

        // Add the HTML version next
        var htmlView = AlternateView.CreateAlternateViewFromString(htmlBody, System.Text.Encoding.UTF8, "text/html");
        mailMessage.AlternateViews.Add(htmlView);

        // Set the Body property as well for clients that don't properly handle AlternateViews
        mailMessage.Body = htmlBody;

        // Add essential anti-spam headers to improve inbox delivery
        mailMessage.Headers.Add("X-Mailer", "Microsoft Outlook");
        mailMessage.Headers.Add("X-Priority", "3"); // Normal priority (1=high can trigger spam filters)
        mailMessage.Headers.Add("Importance", "Normal");
        mailMessage.Headers.Add("X-Auto-Response-Suppress", "OOF, DR, RN, NRN, AutoReply");
        mailMessage.Headers.Add("X-MSMail-Priority", "Normal");

        // Add DKIM-Signature header placeholder - you'll need to implement actual DKIM signing
        // This is just to remind you that DKIM is important for email deliverAbility
        // mailMessage.Headers.Add("DKIM-Signature", "your DKIM signature");

        // Configure the Reply-To address to match the From address
        mailMessage.ReplyToList.Add(new MailAddress(smtpSection["Username"]!, "Carry & Go Support"));

        // Set a message ID with your domain to improve deliverAbility
        var messageId = $"<{Guid.NewGuid()}@{new MailAddress(smtpSection["Username"]!).Host}>";
        mailMessage.Headers.Add("Message-ID", messageId);
        // Add subject line with company name first (improves deliverAbility)
        mailMessage.Subject = "Carry & Go - Password Reset Notification";

        // Set specific content type with proper charset
        mailMessage.BodyEncoding = System.Text.Encoding.UTF8;
        mailMessage.SubjectEncoding = System.Text.Encoding.UTF8;
        
        // Send the email with a proper try-catch block specifically for sending
        smtpClient.Send(mailMessage);
        // Proper disposal of resources
        mailMessage.Dispose();
        smtpClient.Dispose();
        // try
        // {
        //     // Send the email with a proper try-catch block specifically for sending
        //     smtpClient.Send(mailMessage);
        //     logger.LogInformation(
        //         $"Password reset email sent to {entity.Email} at {DateTime.UtcNow}");
        // }
        // catch (SmtpException smtpEx)
        // {
        //     logger.LogError(smtpEx, $"SMTP error sending password reset email to {entity.Email}");
        //     // Still return success since the password was reset successfully
        // }
        // finally
        // {
        //     // Proper disposal of resources
        //     mailMessage.Dispose();
        //     smtpClient.Dispose();
        // }
    }

    public class EmailOtp
    {
        public string? Email { get; init; }
        public string? Password { get; set; }
        public string? UserName { get; set; }
        public string? Otp { get; init; }
    }
}