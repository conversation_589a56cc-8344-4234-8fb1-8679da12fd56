using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Helper
{
    public static class Extention
    {
        /// <summary>
        /// Converts datatable to list<T> dynamically
        /// </summary>
        /// <typeparam name="T">Class name</typeparam>
        /// <param name="dataTable">data table to convert</param>
        /// <returns>List<T></returns>
        public static List<T> ToList<T>(this DataTable dataTable) where T : new()
        {
            var dataList = new List<T>();

            //Define what attributes to be read from the class
            const BindingFlags flags = BindingFlags.Public | BindingFlags.Instance;

            //Read Attribute Names and Types
            var objFieldNames = typeof(T).GetProperties(flags).Cast<PropertyInfo>().
                Select(item => new
                {
                    Name = item.Name,
                    Type = Nullable.GetUnderlyingType(item.PropertyType) ?? item.PropertyType
                }).ToList();

            //Read Datatable column names and types
            var dtlFieldNames = dataTable.Columns.Cast<DataColumn>().
                Select(item => new
                {
                    Name = item.ColumnName,
                    Type = item.DataType
                }).ToList();

            foreach (DataRow dataRow in dataTable.AsEnumerable().ToList())
            {
                var classObj = new T();
                //int i = 0;
                foreach (var dtField in dtlFieldNames)
                {
                    PropertyInfo propertyInfos = classObj.GetType().GetProperty(dtField.Name);

                    var field = objFieldNames.Find(x => x.Name == dtField.Name);

                    if (field != null)
                    {

                        if (propertyInfos.PropertyType == typeof(DateTime))
                        {
                            propertyInfos.SetValue
                            (classObj, Converters.ConvertToDateTime(dataRow[dtField.Name]), null);
                        }
                        else if (propertyInfos.PropertyType == typeof(int))
                        {
                            propertyInfos.SetValue
                            (classObj, Converters.ToInt(dataRow[dtField.Name]), null);
                        }
                        else if (propertyInfos.PropertyType == typeof(long))
                        {
                            propertyInfos.SetValue
                            (classObj, Converters.ToLong(dataRow[dtField.Name]), null);
                        }
                        else if (propertyInfos.PropertyType == typeof(decimal))
                        {
                            propertyInfos.SetValue
                            (classObj, Convert.ToDecimal(dataRow[dtField.Name]), null);
                        }
                        else if (propertyInfos.PropertyType == typeof(String))
                        {
                            propertyInfos.SetValue
                            (classObj, Converters.ToString(dataRow[dtField.Name]), null);
                        }
                    }
                }
                dataList.Add(classObj);
            }
            return dataList;
        }
        public static void DefaulltValuesForModel<T>(this object item) where T : class, new()
        {
            if (item == null)
                return;
            var type = typeof(T);

            var props = type.GetProperties();
            foreach (var prop in props)
            {
                var propType = prop.PropertyType;


                HandelType(prop, item);


            }
        }



        static void HandelType(PropertyInfo prop, object item)
        {
            var propType = prop.PropertyType;

            new Dictionary<Type, Action>{
   {
                    typeof(string),
                    () => {
                            var orgVal = Converters.ToString(prop.GetValue(item));
                            prop.SetValue(item, orgVal);
   } },
   {
                    typeof(decimal),
                    () =>{
                        var orgVal = Converters.ToDecimal(prop.GetValue(item));
                        prop.SetValue(item, orgVal);
        } }
                ,
   {
                    typeof(decimal?),
                    () =>{
                        var orgVal = Converters.ToDecimal(prop.GetValue(item));
                        prop.SetValue(item, orgVal);
        } }
                     ,
   {
                    typeof(DateTime?),
                    () =>{
                        var orgVal = Converters.ConvertToNullableDateTime(prop.GetValue(item));
                        prop.SetValue(item, orgVal);
        } }
                     ,
   {
                    typeof(DateTime),
                    () =>{
                        var orgVal = Converters.ConvertToDateTime(prop.GetValue(item));
                        prop.SetValue(item, orgVal);
        } },
                {
                    typeof(int),
                    () =>{
                        var orgVal = Converters.ConvertToInt(prop.GetValue(item));
                        prop.SetValue(item, orgVal);
        } }
                     ,
                {
                    typeof(float),
                    () =>{
                        var orgVal = Converters.ConvertTofloat(prop.GetValue(item));
                        prop.SetValue(item, orgVal);
        } }
                     ,
                 {
                    typeof(Guid),
                    () =>{
                        var orgVal = Converters.ConvertToGuid(prop.GetValue(item));
                        prop.SetValue(item, orgVal);
        } }
 }[propType]();
        }

        // copy properties value from model to view and vise vers
        public class PropertyMapper<TSource, TDestination> where TSource : class
                                               where TDestination : class
        {
            public static void Copy(TSource source, TDestination destination)
            {
                var sourceProperties = source.GetType().GetProperties();
                var destinationProperties = destination.GetType().GetProperties();
                foreach (var sourceProperty in sourceProperties)
                {
                    foreach (var destinationProperty in destinationProperties)
                    {
                        if (sourceProperty.Name == destinationProperty.Name && sourceProperty.PropertyType == destinationProperty.PropertyType)
                        {
                            destinationProperty.SetValue(destination, sourceProperty.GetValue(source));
                            break;
                        }
                    }
                }
            }
        }

        public static ExpandoObject CreateExpandoFromObject(object source)
        {
            var result = new ExpandoObject();
            IDictionary<string, object> dictionary = result;
            foreach (var property in source
                .GetType()
                .GetProperties()
                .Where(p => p.CanRead && p.GetMethod.IsPublic))
            {
                dictionary[property.Name] = property.GetValue(source, null);
            }
            return result;
        }
        public static dynamic ToDynamic<T>(this T obj)
        {
            IDictionary<string, object> expando = new ExpandoObject();

            foreach (var propertyInfo in typeof(T).GetProperties())
            {
                var currentValue = propertyInfo.GetValue(obj);
                expando.Add(propertyInfo.Name, currentValue);
            }
            return expando as ExpandoObject;
        }
        public static void MatchAndMap<TSource, TDestination>(this TSource source, TDestination destination)
            where TSource : class, new()
            where TDestination : class, new()
        {
            if (source != null && destination != null)
            {
                List<PropertyInfo> sourceProperties = source.GetType().GetProperties().ToList<PropertyInfo>();
                List<PropertyInfo> destinationProperties = destination.GetType().GetProperties().ToList<PropertyInfo>();

                foreach (PropertyInfo sourceProperty in sourceProperties)
                {
                    PropertyInfo destinationProperty = destinationProperties.Find(item => item.Name == sourceProperty.Name);

                    if (destinationProperty != null)
                    {
                        try
                        {
                            destinationProperty.SetValue(destination, sourceProperty.GetValue(source, null), null);
                        }
                        catch (Exception ex)
                        {

                        }
                    }
                }
            }

        }

        public static TDestination MapProperties<TDestination>(this object source)
            where TDestination : class, new()
        {
            var destination = Activator.CreateInstance<TDestination>();
            MatchAndMap(source, destination);

            return destination;
        }

    }
}
