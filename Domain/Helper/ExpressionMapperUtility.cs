using System.Linq.Expressions;

namespace Domain.Helper
{
    public static class ExpressionMapperUtility
    {
        private static readonly Dictionary<string, Delegate> Cache = new();

        public static TDestination? Map<TSource, TDestination>(TSource source) where TDestination : new()
        {
            if (source == null) return default;

            var key = $"{typeof(TSource).FullName}->{typeof(TDestination).FullName}";

            if (Cache.TryGetValue(key, out var cachedFunc)) return ((Func<TSource, TDestination>)cachedFunc)(source);
            var parameter = Expression.Parameter(typeof(TSource), "src");
            var bindings = typeof(TDestination).GetProperties()
                .Where(destProp => destProp.CanWrite)
                .Select(destProp =>
                {
                    var sourceProp = typeof(TSource).GetProperty(destProp.Name);
                    if (sourceProp != null && sourceProp.PropertyType == destProp.PropertyType)
                    {
                        return Expression.Bind(destProp, Expression.Property(parameter, sourceProp));
                    }
                    return null;
                })
                .Where(binding => binding != null);

            var body = Expression.MemberInit(Expression.New(typeof(TDestination)), bindings!);
            var lambda = Expression.Lambda<Func<TSource, TDestination>>(body, parameter);
            cachedFunc = lambda.Compile();
            Cache[key] = cachedFunc;

            return ((Func<TSource, TDestination>)cachedFunc)(source);
        }
    }

}
