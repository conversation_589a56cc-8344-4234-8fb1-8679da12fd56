namespace Domain.ModelDTO;

public class OrderRequestDto
{
    public required string ClientId { get; set; }
    public required string PickupLocation { get; set; }
    public required string PickupCoordinates { get; set; }
    public required string DeliveryLocation { get; set; }
    public required string DeliveryCoordinates { get; set; }
    public required int TransportId { get; set; }
    public string? Destination { get; set; }
    public double? Price { get; set; }
    public string? Description { get; set; }
}