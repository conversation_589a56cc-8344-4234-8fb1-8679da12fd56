using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.ModelDTO
{
    public class UserCompletionStatusDto
    {
        public bool IsComplete { get; set; }
        public int CompletionPercentage { get; set; }
        public List<string> MissingFields { get; set; } = new List<string>();
        public List<string> MissingDocuments { get; set; } = new List<string>();
        public UserCompletionDetails CompletionDetails { get; set; } = new UserCompletionDetails();
    }

    public class UserCompletionDetails
    {
        public bool BasicInfoComplete { get; set; }
        public bool PersonalDetailsComplete { get; set; }
        public bool DriverLicenseComplete { get; set; }
        public bool CarInfoComplete { get; set; }
        public bool CompanyInfoComplete { get; set; }
        public bool DocumentsComplete { get; set; }
    }
}
