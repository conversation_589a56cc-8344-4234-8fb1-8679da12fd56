using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.ModelDTO
{
    public class UserDto
    {
        public required string FirstName { get; set; }
        public required string LastName { get; set; }
        public required string Mobile { get; set; }
        public string? Email { get; set; }
        public  string? Image { get; set; }
        public  string? Password { get; set; }
        public int UserType { get; set; }
        public int? ClientType { get; set; }
        public ClientCompanyInfoDto? ClientInfo { get; set; }
    }
    public class ClientCompanyInfoDto
    {
        public required string Name { get; set; }
        public required string Address { get; set; }
        public string? Description { get; set; }
        public string? CommercialRegisterImg { get; set; }
    }
  
}
