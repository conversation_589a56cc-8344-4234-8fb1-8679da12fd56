using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.ModelDTO
{
    public class DriverDto
    {
        public DriverInfoDto? DriverInfo { get; set; }
        public DriverLicenseDto? DriverLicense { get; set; }
        public DriverCertificateOfGoodConductDto? DriverCertificateOfGoodConduct { get; set; }
        public NationalInfoDto? NationalInfo { get; set; }

        public CarInfoDto? CarInfo { get; set; }
    }
    public class DriverInfoDto
    {
        public required string FirstName { get; set; }
        public required string LastName { get; set; }
        public required string Mobile { get; set; }
        public string? Email { get; set; }
        public  string? Image { get; set; }
        public required string Password { get; set; }
    }
    public class DriverLicenseDto
    {
        public  string LicenseNo { get; set; }
        public  string FrontLicenseImage { get; set; }
        public  string BackLicenseImage { get; set; }
        public  string DateOfExpire { get; set; }
        public  string PersonalLicenseImg { get; set; }
    }
    public class DriverCertificateOfGoodConductDto
    {
        public string CertImage { get; set; }
    }
    public class CarInfoDto
    {
        public  string Transport { get; set; }
        public  string NumberPlate { get; set; }
        public  CertRegistrationDto CertRegistration { get; set; }
        public  string CarImg { get; set; }

    }
    public class CertRegistrationDto
    {
        public string CrcarRegistrationImgF { get; set; }
        public string CrcarRegistrationImgB { get; set; }
        public string CarProductionYear { get; set; }
    }
    public class NationalInfoDto
    {
        public  DateTime BirthDate { get; set; }
        public  string NationalID { get; set; }
        public  string FrontNationalIDImg { get; set; }
        public  string BBackNationalIDImg { get; set; }
        public  string Governorate { get; set; }
        public  string District { get; set; }
        public  string Street { get; set; }
        public  string BuildingNo { get; set; }
    }
}
