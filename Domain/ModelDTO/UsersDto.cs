using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.ModelDTO
{
    public class UsersDto
    {   
        public string? Id { get; set; }
        public string? UsernameEn { get; set; }
        public string? FirstNameEn { get; set; }
        public string? LastNameEn { get; set; }
        public string? UsernameAr { get; set; }
        public string? FirstNameAr { get; set; }
        public string? LastNameAr { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public string? Image { get; set; }
        public int UserType { get; set; }

    }
}
