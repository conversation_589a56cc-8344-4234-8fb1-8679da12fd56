using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.ModelDTO
{
    public class NotificationDTO
    {
        public string title { get; set; }
        public string body { get; set; }
        public string content_available { get; set; }

        public string sound { get; set; }
        // public string click_action { get; set; }
        public string image { get; set; }
        //public string icon { get; set; }

    }
    public class NotificationTextDTO
    {
        public string bodyText { get; set; }
        public string image { get; set; }
        //public string icon { get; set; }

    }

    public class NotificationDataDTO
    {
        public string to { get; set; }
        public int ttl { get; set; }
        public NotificationDTO notification { get; set; }
        public NotificationTextDTO data { get; set; }

    }

    public class NotificationFireBaseDTO
    {
        public List<string> FirebaseToken { get; set; }
        //  public string FirebaseToken { get; set; }
        public string title { get; set; }
        public string body { get; set; }
        public string image { get; set; }

    }

    public class SendNotification
    {
        //public List<string> FirebaseToken { get; set; }
        public string FirebaseToken { get; set; }
        public string title { get; set; }
        public string body { get; set; }
        public string image { get; set; }
        public string ServerKey { get; set; }
        public string SenderId { get; set; }

    }
}
