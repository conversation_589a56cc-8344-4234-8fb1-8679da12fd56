using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.ModelDTO
{
    public class ResultDTO<TEntity>
    {
        public required TEntity Object { get; set; }
        public required string Message { get; set; }
        public bool State { get; set; }
    }
    public class ResultFileDTO
    {

        public required byte[] fileContents { get; set; }
        public required string contentType { get; set; }
        public required string Message { get; set; }
        public string MessageAR { get; set; }
        public bool State { get; set; }
    }
    public class ResultMSG
    {

        public required string MessageEn { get; set; }
        public required string MessageAr { get; set; }
        public bool Status { get; set; }
    }
}
