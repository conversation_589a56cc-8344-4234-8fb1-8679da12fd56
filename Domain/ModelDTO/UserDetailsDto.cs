using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Domain.Entities;

namespace Domain.ModelDTO
{
    public class UserDetailsDto
    {
        public string? Id { get; set; }
        public string? UsernameEn { get; set; }
        public string? FirstNameEn { get; set; }
        public string? LastNameEn { get; set; }
        public string? UsernameAr { get; set; }
        public string? FirstNameAr { get; set; }
        public string? LastNameAr { get; set; }
        public string? Mobile { get; set; }
        public string? Email { get; set; }
        public string? Image { get; set; }
        public int UserType { get; set; }
        public CarInfo? CarInfo { get; set; }
        public DriverLicense? DriverLicense { get; set; }
        public UserDetailsInfo? UserDetailsInfo { get; set; }
    }
}