using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.ModelDTO
{
    public class FcmNotificationDTO
    {
        [JsonProperty("message")]
        public FcmMessage Message { get; set; }
    }

    public class FcmMessage
    {
        [JsonProperty("token", NullValueHandling = NullValueHandling.Ignore)]
        public string? Token { get; set; }

        [JsonProperty("topic", NullValueHandling = NullValueHandling.Ignore)]
        public string? Topic { get; set; }

        [JsonProperty("notification", NullValueHandling = NullValueHandling.Ignore)]
        public Notification? Notification { get; set; }

        [JsonProperty("data", NullValueHandling = NullValueHandling.Ignore)]
        public Dictionary<string, string>? Data { get; set; }
    }

    public class Notification
    {
        [JsonProperty("title")]
        public string Title { get; set; }

        [JsonProperty("body")]
        public string Body { get; set; }
    }

    public class SendFcm
    {
        public string Title { get; set; }
        public string Body { get; set; }
        public string DeviceToken {  get; set; }
    }
}
