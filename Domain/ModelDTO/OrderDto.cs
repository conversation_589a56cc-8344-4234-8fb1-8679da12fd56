namespace Domain.ModelDTO;

public class OrderDto
{
    public int Id { get; set; }
    public required string ClientId { get; set; }
    public required string PickupLocation { get; set; }
    public string? PickupCoordinates { get; set; }
    public string? DeliveryLocation { get; set; }
    public string? DeliveryCoordinates { get; set; }
    public string? Destination { get; set; }
    public double? Price { get; set; }
    public string? Description { get; set; }
    public OrderApprovalDto? OrderApproval { get; set; }
    public TransportDto? Transport { get; set; }
    public string? Status { get; set; }
}