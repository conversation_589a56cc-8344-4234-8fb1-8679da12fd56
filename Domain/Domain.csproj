<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Helper\HelperContext.cs" />
    <Compile Remove="Helper\IHelperContext.cs" />
    <Compile Remove="Helper\ILogger.cs" />
    <Compile Remove="Helper\Logger.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="8.0.6" />
    <PackageReference Include="Microsoft.Owin.Host.SystemWeb" Version="4.2.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NGC.EventLogEntryType" Version="1.0.0" />
  </ItemGroup>

</Project>
