using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.BasicModels
{
    public class ResultM<TEntity>
    {
        public TEntity Object { get; set; }
        public string? Message { get; set; }
        public bool State { get; set; }
        public List<TEntity> Objects { get; set; } // Additional property for lists

    }
}
