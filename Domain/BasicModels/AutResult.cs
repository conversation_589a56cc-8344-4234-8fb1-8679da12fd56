

namespace Domain.BasicModels
{
    public class AutResult
    {
        public string? Token { get; set; }
        public string? Id { get; set; }
        public bool Result { get; set; }
        public List<string>? Errors { get; set; }
        public List<string>? RolesName { get; set; }
        public string? UserName { get; set; }
        public string? Massage { get; set; }
        public object? Data { get; set; }
        public int Type { get; set; }
        public int? ClientType { get; set; }
    }

}
