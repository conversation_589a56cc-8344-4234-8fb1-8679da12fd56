using Domain.BasicModels;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities
{
    public class CarInfo : EntityBase
    {
        //[Key]
        //public int Id { get; set; }
        public required string Transport { get; set; }
        public required string NumberPlate { get; set; }
        public string CrcarRegistrationImgF { get; set; }
        public string CrcarRegistrationImgB { get; set; }
        public string CarProductionYear { get; set; }
        public required string CarImg { get; set; }
        public required string UserId { get; set; }

    }
}
