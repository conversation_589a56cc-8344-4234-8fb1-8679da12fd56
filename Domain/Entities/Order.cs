using System.ComponentModel.DataAnnotations.Schema;
using Domain.BasicModels;
using Domain.EnumsModel;

namespace Domain.Entities;
[Table("order")]
public class Order : EntityBase
{
    public required string ClientId { get; set; }
    public required string PickupLocation { get; set; }
    public required string PickupCoordinates { get; set; }
    public required string DeliveryLocation { get; set; }
    public required string DeliveryCoordinates { get; set; }
    public string? Destination { get; set; }
    public double? Price { get; set; }
    public string? Description { get; set; }
    public OrderApproval? OrderApproval { get; set; }
    
    public required int TransportId { get; set; }
    public Transport Transport { get; set; } = null!;    
    // New property for order status
    public OrderStatus Status { get; set; } = OrderStatus.Pending; // Default value
}