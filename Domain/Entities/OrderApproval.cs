using System.ComponentModel.DataAnnotations.Schema;
using Domain.BasicModels;

namespace Domain.Entities;

[Table("order_approval")]
public class OrderApproval : EntityBase
{
    public required int OrderId { get; set; }
    public Order Order { get; set; }  // Navigation property

    public required string DriverId { get; set; } // Assuming Driver is an ApplicationUser
    public ApplicationUser Driver { get; set; } // Navigation property

    public bool IsApproved { get; set; } = false; // Initially false
    public DateTime? ApprovalDate { get; set; }
}
