using Domain.BasicModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities
{
    public class Otp:EntityBase
    {
        public required string PhoneEmail { get; set; }
        public string? OtpNo { get; set; }
        public string? Firebase { get; set; }
    }
    public class OtpDto
    {
        public required string PhoneEmail { get; set; }
        public string? Firebase { get; set; }
    }
    public class VOtpDto
    {
        public required string PhoneEmail { get; set; }
        public string? OtpNo { get; set; }
    }
}
