using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity;

namespace Domain.Entities
{
    public class ApplicationUser : IdentityUser
    {
      
        [Column(TypeName = "date")]
        public DateTime Creatied_Date { get; set; }
        [StringLength(100)]
        public string CreatedBy { get; set; }
        [Column(TypeName = "date")]
        public DateTime? LastEiteDate { get; set; }
        [StringLength(100)]
        public string? LastEditeBy { get; set; }
        public bool isDeleted { get; set; }
        public bool? state { get; set; }
        public bool? Isapproved { get; set; }
        [StringLength(100)]
        public string? UsernameEn { get; set; }
        [StringLength(100)]
        public string FirstNameEn { get; set; }
        [StringLength(100)]
        public string lastNameEn { get; set; }
        [StringLength(100)]
        public string? UsernameAr { get; set; }
        [StringLength(100)]
        public string FirstNameAr { get; set; }
        [StringLength(100)]
        public string lastNameAr { get; set; }
        [Required]
        public int UserType { get; set; }
        [EmailAddress]
        public string? ExternalEmail { get; set; }
        public string Image { get; set; }
        public string device_token { get; set; }
        public string fingerPrintIdAndroid { get; set; }
        public string path { get; set; }
        public int? ClientType { get; set; }



    }
}
