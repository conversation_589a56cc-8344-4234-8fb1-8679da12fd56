using Domain.BasicModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities
{
    public class DriverLicense : EntityBase
    {
        public  string LicenseNo { get; set; }
        public  string FrontLicenseImage { get; set; }
        public  string BackLicenseImage { get; set; }
        public  string DateOfExpire { get; set; }
        public  string PersonalLicenseImg { get; set; }
        public  string DrCertGConductImage { get; set; }
        public  string UserId { get; set; }
    }

}
