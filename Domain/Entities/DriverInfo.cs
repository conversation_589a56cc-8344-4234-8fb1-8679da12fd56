using Domain.BasicModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Entities
{
    public class DriverInfo : EntityUserBase
    {
        public required string FirstName { get; set; }
        public required string LastName { get; set; }
        public required string Mobile { get; set; }
        public string? Email { get; set; }
        public  string Image { get; set; }
        public  DateTime BirthDate { get; set; }
        public  string Governorate { get; set; }
        public  string District { get; set; }
        public  string Street { get; set; }
        public  string BuildingNo { get; set; }
        public  string NationalID { get; set; }
        public  string FrontNationalIDImg { get; set; }
        public  string BBackNationalIDImg { get; set; }
        public int UserType { get; set; }
    }
    public class AddDriverInfo
    {
        public required string FirstName { get; set; }
        public required string LastName { get; set; }
        public required string BirthDate { get; set; }
        public required string Mobile { get; set; }
        public string? Email { get; set; }
        public  string Image { get; set; }
        public  string Governorate { get; set; }
        public  string District { get; set; }
        public  string Street { get; set; }
        public  string BuildingNo { get; set; }
        public string Password { get; set; }
        public int UserType { get; set; }
        //public string Guid { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int LastUpdateBy { get; set; }
        public DateTime? LastUpdateDate { get; set; }
    }
    public class EditDriverInfo
    {
        public string Id { get; set; }
        public required string FirstName { get; set; }
        public required string LastName { get; set; }
        public  string BirthDate { get; set; }
        public required string Mobile { get; set; }
        public string? Email { get; set; }
        public  string Image { get; set; }
        public  string Governorate { get; set; }
        public  string District { get; set; }
        public  string Street { get; set; }
        public  string BuildingNo { get; set; }

    }
}
