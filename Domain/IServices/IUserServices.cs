using Domain.BasicModels;
using Domain.Entities;
using Domain.ModelDTO;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.IServices
{
    public interface IUserServices 
    {
        public Task<RegistrationRes> Register(DriverDto user);
        public Task<RegistrationRes> ResetPassword(ResetPassword user);
        public Task<RegistrationRes> ForgetPassword(ForgetPassword user);
        public Task Logout();
        public Task<RegistrationRes> Login(LoginRequest user);
        public ResultM<UsersDto> Users();

        public Task<ResultM<ApplicationUser>> CreateUser(DriverInfoDto user);
        public Task<ResultM<UserDetailsDto?>?> Details(string id);
        public Task<ResultM<Dictionary<string, dynamic>>> Update(EditDriverInfo model);
        public Task<ResultM<Dictionary<string, dynamic>>> DeleteUser(string id);
        public Task<ResultM<Dictionary<string, dynamic>>> GetUsersByName(string name);
        Task<Dictionary<string, dynamic>> UploadImge(IFormFile file, string userid,string type);
        Task<ResultFileDTO> Display(string name,string userid);
        Task<RegistrationRes> RegisterClient(UserDto user);
        Task<ResultM<UserDto?>> GetClientDetails(string id);
        Task<ResultM<UserCompletionStatusDto>> CheckUserCompletionStatus(string userId);

    }

}
