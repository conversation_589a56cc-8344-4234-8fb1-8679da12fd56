using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.EnumsModel
{
    public class AllEnumes
    {
        public enum State
        {
            Available = 1,
            reserved = 2,
            Done = 3,
            Reject = 4,
            Pending = 5
        }
        public enum UserType
        {
            Employee = 1,
            Customer = 2,
            MiniEmployee = 3
        }

        public enum StateAppointments
        {
            Pending = 1,
            Canceled = 2,
            Confirmed = 3,
            Completed = 4,
        }
        public enum ShiftTypes
        {
            ShiftA = 1,
            ShiftB = 2
        }
        public enum ShiftTypeValue
        {
            ShiftA = 8,
            ShiftB = 11
        }
    }

}
