# =============================================================================
# Pre-deployment script to kill FTP and conflicting processes
# Run this before your main deployment to prevent "file in use" errors
# =============================================================================

Write-Host "🔧 Running pre-deployment cleanup..." -ForegroundColor Yellow

# Kill FTP processes
Write-Host "Killing FTP processes..." -ForegroundColor Cyan
Get-Process | Where-Object {$_.ProcessName -like "*ftp*"} | Stop-Process -Force -ErrorAction SilentlyContinue

# Kill dotnet processes
Write-Host "Killing .NET processes..." -ForegroundColor Cyan
Get-Process | Where-Object {$_.ProcessName -like "*dotnet*"} | Stop-Process -Force -ErrorAction SilentlyContinue

# Kill API processes
Write-Host "Killing API processes..." -ForegroundColor Cyan
Get-Process | Where-Object {$_.ProcessName -like "*API*"} | Stop-Process -Force -ErrorAction SilentlyContinue

# Clear file handles (if Handle.exe is available)
if (Get-Command "handle.exe" -ErrorAction SilentlyContinue) {
    Write-Host "Clearing file handles..." -ForegroundColor Cyan
    handle.exe -p dotnet -c | Out-Null
}

Write-Host "✅ Pre-deployment cleanup completed!" -ForegroundColor Green
