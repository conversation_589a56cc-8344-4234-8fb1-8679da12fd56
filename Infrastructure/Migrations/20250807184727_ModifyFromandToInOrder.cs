using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class ModifyFromandToInOrder : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ToName",
                table: "order",
                newName: "PickupLocation");

            migrationBuilder.RenameColumn(
                name: "ToLocation",
                table: "order",
                newName: "PickupCoordinates");

            migrationBuilder.RenameColumn(
                name: "FromName",
                table: "order",
                newName: "DeliveryLocation");

            migrationBuilder.RenameColumn(
                name: "FromLocation",
                table: "order",
                newName: "DeliveryCoordinates");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "PickupLocation",
                table: "order",
                newName: "ToName");

            migrationBuilder.RenameColumn(
                name: "PickupCoordinates",
                table: "order",
                newName: "ToLocation");

            migrationBuilder.RenameColumn(
                name: "DeliveryLocation",
                table: "order",
                newName: "FromName");

            migrationBuilder.RenameColumn(
                name: "DeliveryCoordinates",
                table: "order",
                newName: "FromLocation");
        }
    }
}
