using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class changeTransportTypetoId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TransportType",
                table: "order");

            migrationBuilder.AddColumn<int>(
                name: "TransportId",
                table: "order",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_order_TransportId",
                table: "order",
                column: "TransportId");

            migrationBuilder.AddForeignKey(
                name: "FK_order_transport_TransportId",
                table: "order",
                column: "TransportId",
                principalTable: "transport",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_order_transport_TransportId",
                table: "order");

            migrationBuilder.DropIndex(
                name: "IX_order_TransportId",
                table: "order");

            migrationBuilder.DropColumn(
                name: "TransportId",
                table: "order");

            migrationBuilder.AddColumn<string>(
                name: "TransportType",
                table: "order",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }
    }
}
