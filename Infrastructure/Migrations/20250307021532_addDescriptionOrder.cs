using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class addDescriptionOrder : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Description",
                table: "transport");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "transport",
                newName: "Image");

            migrationBuilder.RenameColumn(
                name: "price",
                table: "order",
                newName: "Price");

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "order",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Description",
                table: "order");

            migrationBuilder.RenameColumn(
                name: "Image",
                table: "transport",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "Price",
                table: "order",
                newName: "price");

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "transport",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
