using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure
{
    public class Repository<T> where T : class
    {
        private readonly DriversDBContext context;
        private readonly DbSet<T> entities;

        public Repository(DriversDBContext context)
        {
            this.context = context;
            entities = context.Set<T>();
        }

        public virtual void Add(T entity)
        {
            entities.Add(entity);
            this.context.SaveChanges();
        }
        public virtual void AddtoContext(T entity)
        {
            entities.Add(entity);
        }
        public virtual void RemoveFromContext(T entity)
        {
            entities.Local.Clear();
        }
        public virtual void Add(List<T> entityList)
        {
            for (int i = 0; i < entityList.Count; i++)
            {
                Add(entityList[i]);
            }
        }
        public virtual void Update(T entity)
        {
            entities.Update(entity);
            this.context.SaveChanges();

        }
        public virtual void Delete(T entity)
        {
            entities.Remove(entity);
        }
        public virtual void Delete(int id)
        {
            var entity = GetById(id);
            entities.Remove(entity);
        }
        public virtual T Get(int id)
        {
            return entities.Find(id);
        }
        public virtual T GetById(long id)
        {
            return entities.Find(id);
        }
        //public virtual IEnumerable<T> GetByUserId(string userId)
        //{
        //    return (IEnumerable<T>)entities.Find(userId);
        //}
        public IQueryable<T> GetAllQurAsync()
        {
            return entities;
        }
        public virtual T GetById(Guid id)
        {
            return entities.Find(id);
        }
        public virtual IEnumerable<T> GetAll()
        {
            return entities.ToList();
        }

        public virtual IQueryable<T> GetAllQuery()
        {
            return entities.AsQueryable();
        }
    }

}
