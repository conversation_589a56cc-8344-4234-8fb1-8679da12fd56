using Domain.Entities;
using Domain.IRepo;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure.Repo
{
    public class CarInfoRepository : Repository<CarInfo>, ICarInfoRepository
    {

        private readonly DriversDBContext _driverDbContext;

        public CarInfoRepository(DriversDBContext driverDbContext) : base(driverDbContext)
        {
            _driverDbContext = driverDbContext;
        }
        //public BookingDay CreateBookingDay(BookingDay BookingDay)
        //{
        //    _driversDBContext.BookingDays.Add(BookingDay);
        //    _driversDBContext.SaveChanges();

        //    return BookingDay;
        //}

        //public List<BookingDay> GetAllBookingDays()
        //{
        //    return _driversDBContext.BookingDays.ToList();
        //}
    }

}
