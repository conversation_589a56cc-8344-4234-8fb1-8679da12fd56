using Domain.Entities;
using Domain.IRepo;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure.Repo
{
    public class DriverLicenseRepository : Repository<DriverLicense>, IDriverLicenseRepository
    {

        private readonly DriversDBContext _DriverDBContext;

        public DriverLicenseRepository(DriversDBContext DriverDBContext) : base(DriverDBContext)
        {
            _DriverDBContext = DriverDBContext;
        }
        //public BookingDay CreateBookingDay(BookingDay BookingDay)
        //{
        //    _driversDBContext.BookingDays.Add(BookingDay);
        //    _driversDBContext.SaveChanges();

        //    return BookingDay;
        //}

        //public List<BookingDay> GetAllBookingDays()
        //{
        //    return _driversDBContext.BookingDays.ToList();
        //}
    }

}
