using Domain.Entities;
using Domain.IRepo;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure.Repo
{
    public class ImagesTypesRepository : Repository<ImagesTypes>, IImagesTypesRepository
    {

        private readonly DriversDBContext _DriverDBContext;

        public ImagesTypesRepository(DriversDBContext DriverDBContext) : base(DriverDBContext)
        {
            _DriverDBContext = DriverDBContext;
        }
    }
}