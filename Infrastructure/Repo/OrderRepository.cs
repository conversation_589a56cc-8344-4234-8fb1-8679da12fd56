using Domain.Entities;
using Domain.IRepo;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repo;

public class OrderRepository(DriversDBContext driverDbContext) : Repository<Order>(driverDbContext), IOrderRepository
{
    private readonly DriversDBContext _driverDbContext = driverDbContext;

    public override IEnumerable<Order> GetAll()
    {
        return _driverDbContext.Orders
            .Include(o => o.Transport) // Include Transport details
            .Include(a=>a.OrderApproval)
            .ThenInclude(oa => oa!.Driver) // Ensure Driver is loaded
            .ToList();
    }
}