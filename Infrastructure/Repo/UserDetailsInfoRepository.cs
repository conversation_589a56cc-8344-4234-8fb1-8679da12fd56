using Domain.Entities;
using Domain.IRepo;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure.Repo
{
    public class UserDetailsInfoRepository : Repository<UserDetailsInfo>, IUserDetailsInfoRepository
    {

        private readonly DriversDBContext _DriverDBContext;

        public UserDetailsInfoRepository(DriversDBContext DriverDBContext) : base(DriverDBContext)
        {
            _DriverDBContext = DriverDBContext;
        }
        //public BookingDay CreateBookingDay(BookingDay BookingDay)
        //{
        //    _driversDBContext.BookingDays.Add(BookingDay);
        //    _driversDBContext.SaveChanges();

        //    return BookingDay;
        //}

        //public List<BookingDay> GetAllBookingDays()
        //{
        //    return _driversDBContext.BookingDays.ToList();
        //}
    }

}
