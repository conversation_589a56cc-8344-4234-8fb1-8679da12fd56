using Domain.Entities;
using Domain.IRepo;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repo;

public class OrderApprovalRepository(DriversDBContext driverDbContext)
    : Repository<OrderApproval>(driverDbContext), IOrderApprovalRepository
{
    private readonly DriversDBContext _driverDbContext = driverDbContext;

    public override IEnumerable<OrderApproval> GetAll()
    {
        return _driverDbContext.OrderApprovals
            .Include(o => o.Order) // Include Transport details
            .Include(o => o.Driver) // Include Driver details
            .ToList();
    }
}