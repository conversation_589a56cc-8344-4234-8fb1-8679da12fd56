using Domain.Entities;
using Domain.IRepo;

namespace Infrastructure.Repo;

public class TopicRepository(DriversDBContext driverDbContext)
    : Repository<Topic>(driverDbContext), ITopicRepository
{
    private readonly DriversDBContext _driverDbContext = driverDbContext;

    public IEnumerable<Topic> GetTopicsByType(int type)
    {
        return _driverDbContext.Topics.Where(t => t.Type == type && t.IsActive);
    }

    public IEnumerable<Topic> GetTopicsByClientType(int clientType)
    {
        return _driverDbContext.Topics.Where(t => t.ClientType == clientType && t.IsActive);
    }

    public IEnumerable<Topic> GetActiveTopics()
    {
        return _driverDbContext.Topics.Where(t => t.IsActive);
    }
} 