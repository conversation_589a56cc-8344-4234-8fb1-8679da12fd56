using Domain.Entities;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure
{
    public class DriversDBContext : IdentityDbContext<ApplicationUser>
    {

        public DriversDBContext(DbContextOptions<DriversDBContext> options)
            : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            
            // Relationship: OrderApproval -> Order (One-to-One)
            modelBuilder.Entity<OrderApproval>()
                .HasOne(o => o.Order)
                .WithOne(o => o.OrderApproval)
                .HasForeignKey<OrderApproval>(o => o.OrderId)
                .OnDelete(DeleteBehavior.Cascade);

            // Relationship: OrderApproval -> Driver (One-to-Many)
            modelBuilder.Entity<OrderApproval>()
                .HasOne(o => o.Driver)
                .WithMany() // A driver can approve multiple orders
                .HasForeignKey(o => o.DriverId)
                .OnDelete(DeleteBehavior.Restrict);
            
            modelBuilder.Entity<Order>()
                .HasOne(o => o.Transport)
                .WithMany(t => t.Orders)
                .HasForeignKey(o => o.TransportId)
                .OnDelete(DeleteBehavior.Restrict);

        }
        public DbSet<ApplicationUser> applicationUsers { get; set; }
        public DbSet<Country> countries { get; set; }
        public DbSet<DriverInfo> driverInfos { get; set; }
        public DbSet<DriverLicense> driverLicenses { get; set; }
        public DbSet<CarInfo> carInfos { get; set; }
        public DbSet<UserDetailsInfo> userDetailsInfos { get; set; }
        public DbSet<Otp> otps { get; set; }
        public DbSet<ImagesTypes> imagesTypes { get; set; }
        public DbSet<Setting> Settings { get; set; }
        public DbSet<Transport> Transports { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderApproval> OrderApprovals { get; set; }
        public DbSet<Company> Companies { get; set; }
        public DbSet<Topic> Topics { get; set; }

    }
    //public class DriversDbContextFactory : IDesignTimeDbContextFactory<DriversDBContext>
    //{
    //    public IConfiguration Configuration { get; }

    //    public DriversDbContextFactory(IConfiguration configuration)
    //    {
    //        Configuration = configuration;
    //    }
    //    public DriversDBContext CreateDbContext(string[] args)
    //    {
    //        var optionsBuilder = new DbContextOptionsBuilder<DriversDBContext>();
    //        optionsBuilder.UseSqlServer(Configuration.GetConnectionString("DefaultConnection"));

    //        return new DriversDBContext(optionsBuilder.Options);
    //    }
    //}

}
