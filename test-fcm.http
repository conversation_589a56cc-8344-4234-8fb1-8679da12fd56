### Test FCM Service with <PERSON><PERSON>
POST https://localhost:5001/api/TestFcm/TestNotification
Content-Type: application/json

{
  "deviceToken": "YOUR_DEVICE_TOKEN_HERE",
  "title": "Test Notification",
  "body": "This is a test notification",
  "isSilent": false,
  "data": {
    "action": "test",
    "timestamp": "2025-07-03"
  }
}

### Test FCM Service with Topic
POST https://localhost:5001/api/TestFcm/TestTopicNotification
Content-Type: application/json

{
  "topic": "driver",
  "title": "Test Topic Notification",
  "body": "This is a test topic notification",
  "isSilent": false,
  "data": {
    "action": "background_update",
    "id": "123"
  }
}

### Test Original FCM Endpoint
POST https://localhost:5001/api/Notifications/SendNotificationAsync
Content-Type: application/json

{
  "title": "Test Original",
  "body": "Testing original endpoint",
  "deviceToken": "YOUR_DEVICE_TOKEN_HERE"
}

### Test Silent Notification
POST https://localhost:5001/api/TestFcm/TestNotification
Content-Type: application/json

{
  "deviceToken": "YOUR_DEVICE_TOKEN_HERE",
  "title": "Silent Test",
  "body": "This should be silent",
  "isSilent": true,
  "data": {
    "action": "background_update",
    "silent": "true"
  }
}
