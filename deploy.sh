#!/bin/bash

# =============================================================================
# Carry-GoDriver Deployment Script
# This script handles the complete deployment process including:
# - Killing conflicting processes (FTP, dotnet, docker)
# - Building and deploying the Docker container
# - Cleaning up resources
# =============================================================================

echo "🚀 Starting Carry-GoDriver deployment process..."

# =============================================================================
# STEP 1: Kill conflicting processes
# =============================================================================
echo "🔄 Killing conflicting processes..."

# Kill FTP processes that might be locking files
echo "  - Killing FTP processes..."
pkill -f "ftp" 2>/dev/null || echo "    No FTP processes found"
pkill -f "sftp" 2>/dev/null || echo "    No SFTP processes found"
pkill -f "ftpd" 2>/dev/null || echo "    No FTP daemon processes found"

# Kill dotnet processes in the project directory
echo "  - Killing .NET processes..."
pkill -f "dotnet.*API" 2>/dev/null || echo "    No dotnet API processes found"
pkill -f "dotnet.*Application" 2>/dev/null || echo "    No dotnet Application processes found"

# Kill any processes using files in the current directory
echo "  - Checking for file locks..."
if command -v lsof >/dev/null 2>&1; then
    LOCKED_PIDS=$(lsof +D . 2>/dev/null | grep -E "(API|dotnet)" | awk '{print $2}' | sort -u)
    if [ ! -z "$LOCKED_PIDS" ]; then
        echo "    Found locked processes: $LOCKED_PIDS"
        echo "$LOCKED_PIDS" | xargs kill -9 2>/dev/null || echo "    Some processes may have already exited"
    else
        echo "    No file locks found"
    fi
else
    echo "    lsof not available, skipping file lock check"
fi

# =============================================================================
# STEP 2: Docker cleanup
# =============================================================================
echo "🐳 Cleaning up Docker resources..."

# Stop running containers
echo "  - Stopping running containers..."
docker stop $(docker ps -q) 2>/dev/null || echo "    No running containers to stop"

# Remove old containers
echo "  - Removing old containers..."
docker rm $(docker ps -aq --filter "name=carry-godriver") 2>/dev/null || echo "    No old containers to remove"

# Clean up Docker system
echo "  - Cleaning Docker system..."
docker system prune -f >/dev/null 2>&1 || echo "    Docker cleanup completed"

# =============================================================================
# STEP 3: Build and deploy
# =============================================================================
echo "🔨 Building and deploying application..."

# Build the Docker image
echo "  - Building Docker image..."
if docker-compose build; then
    echo "    ✅ Docker build successful"
else
    echo "    ❌ Docker build failed"
    exit 1
fi

# Deploy the application
echo "  - Starting deployment..."
if docker-compose up -d; then
    echo "    ✅ Deployment successful"
else
    echo "    ❌ Deployment failed"
    exit 1
fi

# =============================================================================
# STEP 4: Verification
# =============================================================================
echo "🔍 Verifying deployment..."

# Wait a moment for containers to start
sleep 5

# Check if containers are running
if docker ps | grep -q "carry-godriver"; then
    echo "    ✅ Containers are running"
    docker ps --filter "name=carry-godriver" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
else
    echo "    ⚠️  No containers found running"
fi

echo "🎉 Deployment process completed!"
echo ""
echo "📋 Next steps:"
echo "   - Check application logs: docker-compose logs -f"
echo "   - Access application at: http://localhost:8080"
echo "   - Monitor containers: docker ps"
