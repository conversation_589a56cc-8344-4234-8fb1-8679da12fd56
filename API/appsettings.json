{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Data Source=sql9001.site4now.net;Database=db_aaf297_carrygodb;User Id=db_aaf297_carrygodb_admin;Password=****************;TrustServerCertificate=True"}, "firebase": {"ServerKey": "", "SenderId": "", "apiKey": ""}, "JWT": {"ValidAudience": "http://localhost:4200", "ValidIssuer": "http://localhost:44326", "Secret": "YhG3MeU2GC6OLKHTQ0MusCz4lZBCbGcrg2cp3GfALQYPnD7okYEr409+oPDXIzEiHiy8auSG9NO49e73DblJqw=="}, "json": {"Version": "2012-10-17", "Id": "Policy1670080669916", "Statement": [{"Sid": "Stmt1670080667795", "Effect": "Allow", "Principal": "*", "Action": "s3:*", "Resource": "arn:aws:s3:::xxx/*"}]}, "AWSAccessKey": "", "AWSSecretKey": "+5GHWsr8", "AzureKey": "", "ImageUrl": "", "OpenAI": {"ApiKey": ""}, "YOUR_ACCOUNT_SID": "", "YOUR_AUTH_TOKEN": "", "DefaultImg": "", "Urllive": "https://easycarrygo.com", "SmtpSettings": {"Host": "smtp.gmail.com", "Port": 587, "Username": "<EMAIL>", "Password": "cprk cttf jvra anub", "EnableSsl": true, "SenderName": "Carry & Go Support", "ReplyToEmail": "<EMAIL>"}}