using Application;
using AutoMapper;
using Domain;
using Domain.Entities;
using Domain.IRepo;
using Domain.IServices;
using Domain.ModelDTO;
using Infrastructure;
using Infrastructure.Repo;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Text;

namespace API
{
    public class Startup(IConfiguration configuration)
    {
        private IConfiguration Configuration { get; } = configuration;
        private string MyAllowSpecificOrigins { get; } = "ApiCorsPolicy";

        private class MappingProfile : Profile
        {
            public MappingProfile()
            {
                CreateMap<DriverLicense, DriverLicenseDto>().ReverseMap();
                CreateMap<CarInfo, CarInfoDto>().ReverseMap();
                CreateMap<UserDetailsInfo, NationalInfoDto>().ReverseMap();
                CreateMap<Otp, OtpDto>().ReverseMap();
                CreateMap<Otp, VOtpDto>().ReverseMap();
                CreateMap<Order, OrderRequestDto>().ReverseMap();

            }
        }
        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddIdentity<ApplicationUser, IdentityRole>().AddDefaultTokenProviders().AddEntityFrameworkStores<DriversDBContext>();
            services.AddControllers();
            services.AddDbContext<DriversDBContext>(options => options.UseSqlServer(Configuration.GetConnectionString("DefaultConnection")));
            services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
            services.AddScoped(typeof(IUserRepository), typeof(UserRepository));
            services.AddTransient<IUserServices, UserServices>();
            services.AddScoped(typeof(IDriverLicenseRepository), typeof(DriverLicenseRepository));
            services.AddScoped(typeof(ICarInfoRepository), typeof(CarInfoRepository));
            services.AddScoped(typeof(IUserDetailsInfoRepository), typeof(UserDetailsInfoRepository));
            services.AddScoped<IOtpRepository, OtpRepository>();
            services.AddTransient<IFcmService, FcmService>();
            services.AddTransient<IOtpServices, OtpServices>();
            services.AddScoped<IImagesTypesRepository, ImagesTypesRepository>();
            services.AddScoped<IImagesTypesService,ImagesTypesService>();
            services.AddScoped<ISettingRepository, SettingRepository>();
            services.AddScoped<ISettingServices, SettingServices>();
            services.AddScoped<ITransportRepository, TransportRepository>();
            services.AddScoped<ITransportServices, TransportServices>();
            services.AddScoped<IOrderRepository, OrderRepository>();
            services.AddScoped<IOrderServices, OrderServices>();
            services.AddScoped<ICompanyInfoRepository, CompanyInfoRepository>();
            services.AddScoped<ITopicRepository, TopicRepository>();
            services.AddScoped<ITopicService, TopicService>();
            services.AddScoped<IOrderApprovalRepository, OrderApprovalRepository>();

            // Adding Authentication  
            // Add AutoMapper and configure it
            services.AddAutoMapper(typeof(MappingProfile));
            // Other service configurations

            //services.AddSwaggerGen();
            services.AddSignalR();

            services.AddSwaggerGen(option =>
            {
                option.SwaggerDoc("v1", new OpenApiInfo { Title = "My API V2", Version = "v1" });
                option.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    In = ParameterLocation.Header,
                    Description = "Please enter a valid token",
                    Name = "Authorization",
                    Type = SecuritySchemeType.Http,
                    BearerFormat = "JWT",
                    Scheme = "Bearer"
                });
                option.AddSecurityRequirement(new OpenApiSecurityRequirement
               {
                 {
                     new OpenApiSecurityScheme
                     {
                         Reference = new OpenApiReference
                         {
                             Type=ReferenceType.SecurityScheme,
                             Id="Bearer"
                         }
                     },
                     []
                 }
           });
            });
            services.AddCors(options =>
            {
                options.AddPolicy(this.MyAllowSpecificOrigins, builder =>
                {
                    builder.WithOrigins("http://localhost:4200", "https://localhost:44326").AllowAnyMethod().AllowAnyHeader().AllowCredentials();
                });
            });

            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
            })

            // Adding Jwt Bearer  
            .AddJwtBearer(options =>
            {
                options.SaveToken = true;
                options.RequireHttpsMetadata = false;
                options.TokenValidationParameters = new TokenValidationParameters()
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidAudience = Configuration["JWT:ValidAudience"],
                    ValidIssuer = Configuration["JWT:ValidIssuer"],
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(Configuration["JWT:Secret"] ?? string.Empty))
                };
            });
            // services.AddControllers().AddJsonOptions(options =>
            // {
            //     options.JsonSerializerOptions.ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.Preserve;
            //     options.JsonSerializerOptions.WriteIndented = true;
            // });
            //var credentials = GoogleCredential.FromFile("radyou-449ed-firebase-adminsdk-q7wi7-697119f61e.json");
            //FirebaseApp.Create(new AppOptions
            //{
            //    Credential = credentials,
            //});

        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseCors(builder => builder.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader());
            app.UseHttpsRedirection();
            app.UseAuthentication();
            app.UseRouting();
            app.UseAuthorization();



            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "My API V2");
            });

            app.UseEndpoints(_ =>
            {
                //endpoints.MapHub<NotificationHub>("/api/NotificationHub");
            });
        }
    }

}
