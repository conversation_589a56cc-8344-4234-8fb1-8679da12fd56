using Application;
using Domain.IServices;
using Domain.ModelDTO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class NotificationsController : ControllerBase
    {
       private readonly IFcmService _fcmService;
        public NotificationsController(IFcmService fcmService)
        {
            _fcmService = fcmService;
        }
        [HttpPost]
        [Route("SendNotificationAsync")]
        public async Task<ActionResult> SendNotificationAsync(SendFcm fcm)
        {
           return Ok(await _fcmService.SendNotificationAsync(fcm.DeviceToken, fcm.Title, fcm.Body));
        }
    }
}
