using Domain.Entities;
using Domain.IServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class TransportsController : ControllerBase
{
    private readonly ITransportServices _transportServices;
    public Dictionary<string, string> ResDictionary = new() { };

    public TransportsController(ITransportServices transportServices)
    {
        _transportServices = transportServices;
    }

    [HttpGet("GetTransports")]
    public ActionResult GetTransports()
    {
        return Ok(_transportServices.GetAll());
    }

    [HttpPost("Transport")]
    public ActionResult Settings([FromBody] Transport entity)
    {
        _transportServices.Add(entity);
        return Ok(entity);
    }
}