using Application;
using Domain.IServices;
using Domain.ModelDTO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TestFcmController : ControllerBase
    {
        private readonly IFcmService _fcmService;
        
        public TestFcmController(IFcmService fcmService)
        {
            _fcmService = fcmService;
        }

        [HttpPost]
        [Route("TestNotification")]
        public async Task<ActionResult> TestNotification([FromBody] TestFcmRequest request)
        {
            try
            {
                var result = await _fcmService.SendNotificationAsync(
                    request.DeviceToken, 
                    request.Title, 
                    request.Body,
                    request.IsSilent,
                    request.Topic,
                    request.Data
                );

                return Ok(new { success = true, result = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, error = ex.Message });
            }
        }

        [HttpPost]
        [Route("TestTopicNotification")]
        public async Task<ActionResult> TestTopicNotification([FromBody] TestTopicRequest request)
        {
            try
            {
                var result = await _fcmService.SendNotificationAsync(
                    null, // no device token
                    request.Title, 
                    request.Body,
                    request.IsSilent,
                    request.Topic,
                    request.Data
                );

                return Ok(new { success = true, result = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, error = ex.Message });
            }
        }
    }

    public class TestFcmRequest
    {
        public string DeviceToken { get; set; } = "";
        public string Title { get; set; } = "";
        public string Body { get; set; } = "";
        public bool IsSilent { get; set; } = false;
        public string? Topic { get; set; }
        public object? Data { get; set; }
    }

    public class TestTopicRequest
    {
        public string Topic { get; set; } = "";
        public string Title { get; set; } = "";
        public string Body { get; set; } = "";
        public bool IsSilent { get; set; } = false;
        public object? Data { get; set; }
    }
}
