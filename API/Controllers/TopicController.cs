using Domain.Entities;
using Domain.IServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class TopicController: ControllerBase
{
    private readonly ITopicService _topicService;

    public TopicController(ITopicService topicService )
    {
        _topicService = topicService;
    }
    [HttpGet]
    public ActionResult<IEnumerable<Topic>> GetAll()
    {
        return Ok(_topicService.GetAll());
    }

    [HttpGet("{id:int}")]
    public ActionResult<Topic> GetById(int id)
    {
        var topic = _topicService.GetById(id);
        return Ok(topic);
    }

    [HttpPost]
    // [Authorize(Roles = "Admin")]
    public ActionResult<Topic> Create(Topic topic)
    {
        _topicService.Add(topic);
        return CreatedAtAction(nameof(GetById), new { id = topic.Id }, topic);
    }

    [HttpPut("{id:int}")]
    // [Authorize(Roles = "Admin")]
    public IActionResult Update(int id, Topic topic)
    {
        if (id != topic.Id)
            return BadRequest();

        var existingTopic = _topicService.GetById(id);
        if (existingTopic == null)
            return NotFound();

        _topicService.Update(topic);
        return NoContent();
    }

    [HttpDelete("{id:int}")]
    // [Authorize(Roles = "Admin")]
    public IActionResult Delete(int id)
    {
        var topic = _topicService.GetById(id);
        if (topic == null)
            return NotFound();

        _topicService.Delete(id);
        return NoContent();
    }

    [HttpGet("type/{type:int}")]
    public ActionResult<IEnumerable<Topic>> GetByType(int type)
    {
        return Ok(_topicService.GetTopicsByType(type));
    }

    [HttpGet("client-type/{clientType:int}")]
    public ActionResult<IEnumerable<Topic>> GetByClientType(int clientType)
    {
        return Ok(_topicService.GetTopicsByClientType(clientType));
    }

    [HttpGet("active")]
    public ActionResult<IEnumerable<Topic>> GetActive()
    {
        return Ok(_topicService.GetActiveTopics());
    }
} 