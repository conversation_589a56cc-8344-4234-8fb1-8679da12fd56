using Application;
using AutoMapper;
using Domain.IServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ImagesController : ControllerBase
    {
        private readonly ILogger<AccountsController> _logger;
        private readonly IImagesTypesService _imagesTypesService;
        public Dictionary<string, string> resDictionary = new Dictionary<string, string>() { };
        public Dictionary<string, dynamic> listDictionary = new Dictionary<string, dynamic>() { };

        public ImagesController(ILogger<AccountsController> logger, IImagesTypesService imagesTypesService)
        {
            _logger = logger;
            _imagesTypesService = imagesTypesService;
        }

        [HttpGet("GetImagesTypes")]
        public ActionResult GetImagesTypes()
        {
            var List = _imagesTypesService.GetAll();
            if (List.Count() != 0)
            {
                return Ok(List);
            }
            else
            {
                _logger.LogError("GetImagesTypes Errors", List);

                return Content("List Of  Images Types are Empety");
            }
        }

        [HttpGet("Display")]
        public async Task<IActionResult> Display(string name)
        {
            var Result = await _imagesTypesService.Display(name);
            if (Result.State)
            {
                return File(Result.fileContents, Result.contentType);
            }
            else
            {
                resDictionary.Add("Message", Result.Message);
                resDictionary.Add("MessageAR", Result.MessageAR);
                return BadRequest(resDictionary);
            }
        }
    }
}
