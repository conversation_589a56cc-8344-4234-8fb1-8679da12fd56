using Domain.Entities;
using Domain.IServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class SettingsController : ControllerBase
{
    private readonly ISettingServices _settingServices;
    public Dictionary<string, string> ResDictionary = new() { };

    public SettingsController(ISettingServices settingServices)
    {
        _settingServices = settingServices;
    }

    [HttpGet("GetSettings")]
    public ActionResult GetSettings()
    {
        return Ok(_settingServices.GetAll().FirstOrDefault() ?? new Setting
        {
            Distance = string.Empty,
            Price = 0.0,
            RequiredDistance = 0.0
        });
    }

    [HttpPost("Settings")]
    public ActionResult Settings([FromBody] Setting setting)
    {
        _settingServices.Add(setting);
        return Ok(setting);
    }
}