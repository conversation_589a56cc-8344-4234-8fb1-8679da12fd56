# =============================================================================
# Carry-GoDriver Deployment Script for PowerShell
# This script handles the complete deployment process including:
# - Killing conflicting processes (FTP, dotnet, docker)
# - Building and deploying the Docker container
# - Cleaning up resources
# =============================================================================

Write-Host "🚀 Starting Carry-GoDriver deployment process..." -ForegroundColor Green

# =============================================================================
# STEP 1: Kill conflicting processes
# =============================================================================
Write-Host "🔄 Killing conflicting processes..." -ForegroundColor Yellow

# Kill FTP processes that might be locking files
Write-Host "  - Killing FTP processes..." -ForegroundColor Cyan
try {
    Get-Process | Where-Object {$_.ProcessName -like "*ftp*"} | Stop-Process -Force -ErrorAction SilentlyContinue
    Get-Process | Where-Object {$_.ProcessName -like "*sftp*"} | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "    ✅ FTP processes killed" -ForegroundColor Green
} catch {
    Write-Host "    ℹ️ No FTP processes found" -ForegroundColor Gray
}

# Kill dotnet processes
Write-Host "  - Killing .NET processes..." -ForegroundColor Cyan
try {
    Get-Process | Where-Object {$_.ProcessName -like "*dotnet*" -or $_.ProcessName -like "*API*"} | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "    ✅ .NET processes killed" -ForegroundColor Green
} catch {
    Write-Host "    ℹ️ No .NET processes found" -ForegroundColor Gray
}

# Kill processes using files in current directory (Windows equivalent of lsof)
Write-Host "  - Checking for file locks..." -ForegroundColor Cyan
try {
    $currentPath = Get-Location
    $processes = Get-Process | Where-Object {$_.Path -like "$currentPath*"}
    if ($processes) {
        $processes | Stop-Process -Force -ErrorAction SilentlyContinue
        Write-Host "    ✅ File locks cleared" -ForegroundColor Green
    } else {
        Write-Host "    ℹ️ No file locks found" -ForegroundColor Gray
    }
} catch {
    Write-Host "    ⚠️ Could not check file locks" -ForegroundColor Yellow
}

# =============================================================================
# STEP 2: Docker cleanup
# =============================================================================
Write-Host "🐳 Cleaning up Docker resources..." -ForegroundColor Yellow

# Stop running containers
Write-Host "  - Stopping running containers..." -ForegroundColor Cyan
try {
    $runningContainers = docker ps -q
    if ($runningContainers) {
        docker stop $runningContainers 2>$null
        Write-Host "    ✅ Containers stopped" -ForegroundColor Green
    } else {
        Write-Host "    ℹ️ No running containers to stop" -ForegroundColor Gray
    }
} catch {
    Write-Host "    ℹ️ No running containers found" -ForegroundColor Gray
}

# Remove old containers
Write-Host "  - Removing old containers..." -ForegroundColor Cyan
try {
    $oldContainers = docker ps -aq --filter "name=carry-godriver"
    if ($oldContainers) {
        docker rm $oldContainers 2>$null
        Write-Host "    ✅ Old containers removed" -ForegroundColor Green
    } else {
        Write-Host "    ℹ️ No old containers to remove" -ForegroundColor Gray
    }
} catch {
    Write-Host "    ℹ️ No old containers found" -ForegroundColor Gray
}

# Clean up Docker system
Write-Host "  - Cleaning Docker system..." -ForegroundColor Cyan
try {
    docker system prune -f | Out-Null
    Write-Host "    ✅ Docker system cleaned" -ForegroundColor Green
} catch {
    Write-Host "    ⚠️ Docker cleanup completed with warnings" -ForegroundColor Yellow
}

# =============================================================================
# STEP 3: Build and deploy
# =============================================================================
Write-Host "🔨 Building and deploying application..." -ForegroundColor Yellow

# Build the Docker image
Write-Host "  - Building Docker image..." -ForegroundColor Cyan
try {
    $buildResult = docker-compose build 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "    ✅ Docker build successful" -ForegroundColor Green
    } else {
        Write-Host "    ❌ Docker build failed" -ForegroundColor Red
        Write-Host $buildResult -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "    ❌ Docker build failed with exception" -ForegroundColor Red
    exit 1
}

# Deploy the application
Write-Host "  - Starting deployment..." -ForegroundColor Cyan
try {
    $deployResult = docker-compose up -d 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "    ✅ Deployment successful" -ForegroundColor Green
    } else {
        Write-Host "    ❌ Deployment failed" -ForegroundColor Red
        Write-Host $deployResult -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "    ❌ Deployment failed with exception" -ForegroundColor Red
    exit 1
}

# =============================================================================
# STEP 4: Verification
# =============================================================================
Write-Host "🔍 Verifying deployment..." -ForegroundColor Yellow

# Wait a moment for containers to start
Start-Sleep -Seconds 5

# Check if containers are running
Write-Host "  - Checking container status..." -ForegroundColor Cyan
try {
    $containers = docker ps --filter "name=carry-godriver" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    if ($containers) {
        Write-Host "    ✅ Containers are running:" -ForegroundColor Green
        Write-Host $containers -ForegroundColor White
    } else {
        Write-Host "    ⚠️ No containers found running" -ForegroundColor Yellow
    }
} catch {
    Write-Host "    ⚠️ Could not verify container status" -ForegroundColor Yellow
}

Write-Host "🎉 Deployment process completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "   - Check application logs: docker-compose logs -f" -ForegroundColor White
Write-Host "   - Access application at: http://localhost:8080" -ForegroundColor White
Write-Host "   - Monitor containers: docker ps" -ForegroundColor White

# Optional: Open browser to application
$openBrowser = Read-Host "Would you like to open the application in your browser? (y/n)"
if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
    Start-Process "http://localhost:8080"
}
