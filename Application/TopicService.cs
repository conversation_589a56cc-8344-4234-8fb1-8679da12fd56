using Domain.Entities;
using Domain.IRepo;
using Domain.IServices;

namespace Application;

public class TopicService : ITopicService
{
    private readonly ITopicRepository _topicRepository;

    public TopicService(ITopicRepository topicRepository)
    {
        _topicRepository = topicRepository;
    }

    public IEnumerable<Topic> GetAll()
    {
        return _topicRepository.GetAll();
    }

    public Topic GetById(int id)
    {
        return _topicRepository.Get(id);
    }

    public void Add(Topic entity)
    {
        entity.CreatedAt = DateTime.UtcNow;
        _topicRepository.Add(entity);
    }

    public void Update(Topic entity)
    {
        entity.UpdatedAt = DateTime.UtcNow;
        _topicRepository.Update(entity);
    }

    public void Delete(Topic entity)
    {
        throw new NotImplementedException();
    }

    public void Delete(int id)
    {
        _topicRepository.Delete(id);
    }

    public IQueryable<Topic> GetAllQuery(Guid UserId)
    {
        throw new NotImplementedException();
    }

    public IEnumerable<Topic> GetTopicsByType(int type)
    {
        return _topicRepository.GetTopicsByType(type);
    }

    public IEnumerable<Topic> GetTopicsByClientType(int clientType)
    {
        return _topicRepository.GetTopicsByClientType(clientType);
    }

    public IEnumerable<Topic> GetActiveTopics()
    {
        return _topicRepository.GetActiveTopics();
    }
} 