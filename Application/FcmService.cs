using Domain.IServices;
using Domain.ModelDTO;
using Google.Apis.Auth.OAuth2;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Text;
using System.Net;


namespace Application
{
    public class FcmService : IFcmService
    {
        private const string FcmEndpoint = "https://fcm.googleapis.com/v1/projects/caryandgo-3dbda/messages:send";
        private readonly string _serviceAccountJsonPath = Path.Combine(Directory.GetCurrentDirectory(), "caryandgo-3dbda-8f8a2f78faa0.json");

        public async Task<string> SendNotificationAsync(string deviceToken, string title, string body,
            bool isSilent = false, string? topic = null, object? data = null)
        {
            // Validate inputs
            if (string.IsNullOrEmpty(deviceToken) && string.IsNullOrEmpty(topic))
            {
                return "Error: Either deviceToken or topic must be provided";
            }

            if (!string.IsNullOrEmpty(deviceToken) && !IsValidDeviceToken(deviceToken))
            {
                return "Error: Invalid device token format";
            }

            GoogleCredential credential;

            try
            {
                await using var stream = new FileStream(_serviceAccountJsonPath, FileMode.Open, FileAccess.Read);
                credential = GoogleCredential.FromStream(stream)
                    .CreateScoped("https://www.googleapis.com/auth/firebase.messaging");
            }
            catch (Exception ex)
            {
                return "Error reading service account file: " + ex.Message;
            }

            string accessToken;
            try
            {
                accessToken = await credential.UnderlyingCredential.GetAccessTokenForRequestAsync();
            }
            catch (Exception ex)
            {
                return "Error obtaining access token: " + ex.Message;
            }

            using (var httpClient = new HttpClient())
            {
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                var message = new FcmMessage();

                // Set either token or topic based on what's provided
                if (!string.IsNullOrEmpty(deviceToken))
                {
                    message.Token = deviceToken;
                }
                else if (!string.IsNullOrEmpty(topic))
                {
                    message.Topic = topic;
                }

                // Convert data to string key-value pairs as required by FCM v1 API
                if (data != null)
                {
                    message.Data = ConvertDataToStringDictionary(data);
                }

                // Only add notification if not silent
                if (!isSilent)
                {
                    message.Notification = new Notification
                    {
                        Title = title,
                        Body = body,
                    };
                }

                var notification = new FcmNotificationDTO
                {
                    Message = message
                };

                var jsonSettings = new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore,
                    Formatting = Formatting.Indented
                };

                var json = JsonConvert.SerializeObject(notification, jsonSettings);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Log the request for debugging
                Console.WriteLine($"FCM Request JSON: {json}");

                try
                {
                    var response = await httpClient.PostAsync(FcmEndpoint, content);
                    var responseContent = await response.Content.ReadAsStringAsync();

                    // Log the response for debugging
                    Console.WriteLine($"FCM Response Status: {response.StatusCode}");
                    Console.WriteLine($"FCM Response Content: {responseContent}");

                    if (response.IsSuccessStatusCode)
                    {
                        return responseContent;
                    }
                    else
                    {
                        return $"FCM Error {(int)response.StatusCode} ({response.StatusCode}): {responseContent}";
                    }
                }
                catch (HttpRequestException httpEx)
                {
                    return "HTTP request error: " + httpEx.Message;
                }
                catch (Exception ex)
                {
                    return "Unexpected error: " + ex.Message;
                }
            }
        }

        private bool IsValidDeviceToken(string deviceToken)
        {
            // Basic validation for FCM device token
            // FCM tokens are typically 152+ characters long and contain alphanumeric characters, hyphens, and underscores
            if (string.IsNullOrWhiteSpace(deviceToken))
                return false;

            if (deviceToken.Length < 140) // FCM tokens are usually longer than 140 characters
                return false;

            // Check if token contains only valid characters (alphanumeric, hyphens, underscores, colons)
            return System.Text.RegularExpressions.Regex.IsMatch(deviceToken, @"^[a-zA-Z0-9_:-]+$");
        }

        private Dictionary<string, string> ConvertDataToStringDictionary(object data)
        {
            var result = new Dictionary<string, string>();

            if (data == null)
                return result;

            // If it's already a dictionary of strings, return it
            if (data is Dictionary<string, string> stringDict)
                return stringDict;

            // If it's a dictionary with object values, convert to strings
            if (data is Dictionary<string, object> objectDict)
            {
                foreach (var kvp in objectDict)
                {
                    result[kvp.Key] = kvp.Value?.ToString() ?? "";
                }
                return result;
            }

            // For complex objects, serialize to JSON and then parse as key-value pairs
            try
            {
                var json = JsonConvert.SerializeObject(data);
                var dict = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);

                if (dict != null)
                {
                    foreach (var kvp in dict)
                    {
                        // Convert complex values to JSON strings
                        if (kvp.Value is string str)
                        {
                            result[kvp.Key] = str;
                        }
                        else
                        {
                            result[kvp.Key] = JsonConvert.SerializeObject(kvp.Value);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Fallback: add the entire object as a JSON string
                result["data"] = JsonConvert.SerializeObject(data);
                Console.WriteLine($"Warning: Could not parse data object, using fallback: {ex.Message}");
            }

            return result;
        }

    }

}
