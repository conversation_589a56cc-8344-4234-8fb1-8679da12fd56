using Domain.Entities;
using Domain.IRepo;
using Domain.IServices;
using Domain.ModelDTO;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application
{
    public class ImagesTypesService : IImagesTypesService
    {
        private readonly IImagesTypesRepository _imagesTypesRepository;
        private readonly IConfiguration _Configure;

        public ImagesTypesService(IConfiguration configure, IImagesTypesRepository imagesTypesRepository)
        {
            _imagesTypesRepository = imagesTypesRepository;
            _Configure = configure;
        }

        public void Add(ImagesTypes entity)
        {
            throw new NotImplementedException();
        }

        public void Delete(ImagesTypes entity)
        {
            throw new NotImplementedException();
        }

        public void Delete(int id)
        {
            throw new NotImplementedException();
        }

        public async Task<ResultFileDTO> Display(string name)
        {
            var Obj = new ResultFileDTO()
            {
                contentType = "",
                fileContents = Array.Empty<byte>(),
                Message = "",
                MessageAR = ""
            };
            
            if (name == null)
            {
                Obj = new ResultFileDTO()
                {
                    contentType = "none",
                    fileContents = Array.Empty<byte>(),
                    Message = "name not present",
                    MessageAR = "برجاء ادخال الاسم",
                    State = false
                };
                return Obj;
            }
            var FileExtension = Path.GetExtension(name);
            var path = Path.Combine(@"uploads/", name);
            string filepath = Path.Combine(path);
            byte[] imageArry = System.IO.File.ReadAllBytes(filepath.Replace("//", "\\"));
            string base64ImageRepresantation = Convert.ToBase64String(imageArry);
            byte[] Picture = Convert.FromBase64String(base64ImageRepresantation);
            string type = "image/" + FileExtension.Split('.')[1];
            string Message = "name present";
            string MessageAR = "تم ايجاد الاسم";
            Obj = new ResultFileDTO()
            {
                contentType = type,
                fileContents = Picture,
                Message = Message,
                MessageAR = MessageAR,
                State = true
            };
            return Obj;
        }


        public IEnumerable<ImagesTypes> GetAll()
        {
          return _imagesTypesRepository.GetAll();
        }

        public IQueryable<ImagesTypes> GetAllQuery(Guid UserId)
        {
            throw new NotImplementedException();
        }

        public ImagesTypes GetById(int id)
        {
            throw new NotImplementedException();
        }

        public void Update(ImagesTypes entity)
        {
            throw new NotImplementedException();
        }
    }
}
