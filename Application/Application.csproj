<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="caryandgo-3dbda-8f8a2f78faa0.json" />
    <None Remove="caryandgo-86021-b8e8d4b55932.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Google.Apis.Auth" Version="1.68.0" />
    <PackageReference Include="Google.Apis.Core" Version="1.68.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.6" />
    <PackageReference Include="MimeKit" Version="4.12.0" />
    <PackageReference Include="Nancy" Version="2.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.6" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.1.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Domain\Domain.csproj" />
  </ItemGroup>

</Project>
