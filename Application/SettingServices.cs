using Domain.Entities;
using Domain.IRepo;
using Domain.IServices;

namespace Application;

public class SettingServices : ISettingServices
{
    private readonly ISettingRepository _settingRepository;

    public SettingServices(ISettingRepository settingRepository)
    {
        _settingRepository = settingRepository;
    }

    public IEnumerable<Setting> GetAll()
    {
        return _settingRepository.GetAll();
    }

    public Setting GetById(int id)
    {
        throw new NotImplementedException();
    }

    public void Add(Setting entity)
    {
        _settingRepository.Add(entity);
    }

    public void Update(Setting entity)
    {
        _settingRepository.Update(entity);
    }

    public void Delete(Setting entity)
    {
        _settingRepository.Delete(entity);
    }

    public void Delete(int id)
    {
        _settingRepository.Delete(id);
    }

    public IQueryable<Setting> GetAllQuery(Guid UserId)
    {
        throw new NotImplementedException();
    }
}