using Domain.Entities;
using Domain.IRepo;
using Domain.IServices;

namespace Application;

public class TransportServices: ITransportServices
{
    ITransportRepository _transportRepository;
    public TransportServices(ITransportRepository transportRepository)
    {
        _transportRepository = transportRepository;
    }
    public IEnumerable<Transport> GetAll()
    {
        return _transportRepository.GetAll();
    }

    public Transport GetById(int id)
    {
        return _transportRepository.GetById(id);
    }

    public void Add(Transport entity)
    {
        _transportRepository.Add(entity);
    }

    public void Update(Transport entity)
    {
        _transportRepository.Update(entity);
    }

    public void Delete(Transport entity)
    {
        _transportRepository.Delete(entity);
    }

    public void Delete(int id)
    {
       _transportRepository.Delete(id);
    }

    public IQueryable<Transport> GetAllQuery(Guid UserId)
    {
        throw new NotImplementedException();
    }
}