@echo off
REM =============================================================================
REM Carry-GoDriver Deployment Script for Windows
REM This script handles the complete deployment process including:
REM - Killing conflicting processes (FTP, dotnet, docker)
REM - Building and deploying the Docker container
REM - Cleaning up resources
REM =============================================================================

echo 🚀 Starting Carry-GoDriver deployment process...

REM =============================================================================
REM STEP 1: Kill conflicting processes
REM =============================================================================
echo 🔄 Killing conflicting processes...

REM Kill FTP processes that might be locking files
echo   - Killing FTP processes...
taskkill /F /IM ftp.exe 2>nul || echo     No FTP processes found
taskkill /F /IM sftp.exe 2>nul || echo     No SFTP processes found
taskkill /F /IM ftpd.exe 2>nul || echo     No FTP daemon processes found

REM Kill dotnet processes
echo   - Killing .NET processes...
taskkill /F /IM dotnet.exe 2>nul || echo     No dotnet processes found
taskkill /F /IM API.exe 2>nul || echo     No API processes found

REM Kill processes by name pattern
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq dotnet.exe" /FO CSV ^| find "dotnet.exe"') do (
    taskkill /F /PID %%i 2>nul
)

REM =============================================================================
REM STEP 2: Docker cleanup
REM =============================================================================
echo 🐳 Cleaning up Docker resources...

REM Stop running containers
echo   - Stopping running containers...
for /f %%i in ('docker ps -q') do docker stop %%i 2>nul

REM Remove old containers
echo   - Removing old containers...
for /f %%i in ('docker ps -aq --filter "name=carry-godriver"') do docker rm %%i 2>nul

REM Clean up Docker system
echo   - Cleaning Docker system...
docker system prune -f >nul 2>&1

REM =============================================================================
REM STEP 3: Build and deploy
REM =============================================================================
echo 🔨 Building and deploying application...

REM Build the Docker image
echo   - Building Docker image...
docker-compose build
if %ERRORLEVEL% NEQ 0 (
    echo     ❌ Docker build failed
    exit /b 1
) else (
    echo     ✅ Docker build successful
)

REM Deploy the application
echo   - Starting deployment...
docker-compose up -d
if %ERRORLEVEL% NEQ 0 (
    echo     ❌ Deployment failed
    exit /b 1
) else (
    echo     ✅ Deployment successful
)

REM =============================================================================
REM STEP 4: Verification
REM =============================================================================
echo 🔍 Verifying deployment...

REM Wait a moment for containers to start
timeout /t 5 /nobreak >nul

REM Check if containers are running
docker ps --filter "name=carry-godriver" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo 🎉 Deployment process completed!
echo.
echo 📋 Next steps:
echo    - Check application logs: docker-compose logs -f
echo    - Access application at: http://localhost:8080
echo    - Monitor containers: docker ps

pause
