### Test User Completion Status Endpoint

# First, login to get a token
POST {{baseUrl}}/api/Accounts/Login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "yourpassword",
  "device_token": "test-device-token"
}

### Check User Completion Status (Driver)
GET {{baseUrl}}/api/Accounts/CheckCompletionStatus
Authorization: Bearer {{token}}

### Check User Completion Status (Client)
GET {{baseUrl}}/api/Accounts/CheckCompletionStatus
Authorization: Bearer {{client_token}}

### Expected Response Format:
# {
#   "isComplete": false,
#   "completionPercentage": 60,
#   "missingFields": [
#     "Birth Date",
#     "National ID",
#     "License Number"
#   ],
#   "missingDocuments": [
#     "Front National ID Image",
#     "Back National ID Image",
#     "Front License Image"
#   ],
#   "completionDetails": {
#     "basicInfoComplete": true,
#     "personalDetailsComplete": false,
#     "driverLicenseComplete": false,
#     "carInfoComplete": true,
#     "companyInfoComplete": true,
#     "documentsComplete": false
#   }
# }
